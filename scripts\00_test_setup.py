#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Setup Test Script / 设置测试脚本

This script tests the basic setup and configuration of the GRAG system.
该脚本测试GRAG系统的基本设置和配置。
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

def test_imports():
    """Test if all modules can be imported / 测试是否所有模块都可以导入"""
    print("Testing imports...")

    try:
        # Test utils
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

        from utils import config_manager, get_logger, set_seed
        print("✓ Utils imported successfully")

        # Test data processing
        from data_processing import MedicalDatasetLoader, MedicalTextPreprocessor, MedicalDataAugmenter
        print("✓ Data processing modules imported successfully")

        # Test retrieval
        from retrieval import MedicalVectorStore, MedicalRetriever, MedicalReranker, MedicalWebSearcher
        print("✓ Retrieval modules imported successfully")

        return True

    except Exception as e:
        print(f"✗ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config():
    """Test configuration loading / 测试配置加载"""
    print("\nTesting configuration...")

    try:
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

        from utils import config_manager

        # Test basic config access
        model_config = config_manager.model
        print(f"✓ Model config loaded: {model_config.base_model}")

        data_config = config_manager.data
        print(f"✓ Data config loaded: {data_config.dataset_name}")

        retrieval_config = config_manager.retrieval
        print(f"✓ Retrieval config loaded: {retrieval_config.embedding['model_name']}")

        return True

    except Exception as e:
        print(f"✗ Config test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_logger():
    """Test logger functionality / 测试日志功能"""
    print("\nTesting logger...")

    try:
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

        from utils import get_logger

        logger = get_logger("test")
        logger.info("Test log message")
        logger.warning("Test warning message")

        print("✓ Logger working correctly")
        return True

    except Exception as e:
        print(f"✗ Logger test failed: {e}")
        return False


def test_text_processing():
    """Test text processing functionality / 测试文本处理功能"""
    print("\nTesting text processing...")

    try:
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

        from utils import clean_text, extract_medical_entities
        from data_processing import MedicalTextPreprocessor

        # Test text cleaning
        test_text = "  这是一个测试文本，包含头痛和发烧症状。  "
        cleaned = clean_text(test_text)
        print(f"✓ Text cleaning: '{test_text}' -> '{cleaned}'")

        # Test medical entity extraction
        entities = extract_medical_entities(test_text)
        print(f"✓ Medical entities extracted: {entities}")

        # Test preprocessor
        preprocessor = MedicalTextPreprocessor()
        processed = preprocessor.preprocess_text(test_text)
        print(f"✓ Text preprocessing: '{processed}'")

        return True

    except Exception as e:
        print(f"✗ Text processing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_environment():
    """Test environment setup / 测试环境设置"""
    print("\nTesting environment...")

    try:
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

        from utils import get_device, set_seed

        # Test device detection
        device = get_device()
        print(f"✓ Device detected: {device}")

        # Test seed setting
        set_seed(42)
        print("✓ Random seed set successfully")

        return True

    except Exception as e:
        print(f"✗ Environment test failed: {e}")
        return False


def test_directories():
    """Test directory structure / 测试目录结构"""
    print("\nTesting directory structure...")
    
    required_dirs = [
        "data/raw",
        "data/processed", 
        "data/embeddings",
        "models/sft",
        "models/grpo",
        "models/reward",
        "logs"
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            missing_dirs.append(dir_path)
    
    if missing_dirs:
        print(f"✗ Missing directories: {missing_dirs}")
        print("Creating missing directories...")
        for dir_path in missing_dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
        print("✓ Directories created")
    else:
        print("✓ All required directories exist")
    
    return True


def test_dependencies():
    """Test key dependencies / 测试关键依赖"""
    print("\nTesting dependencies...")
    
    dependencies = [
        ("torch", "PyTorch"),
        ("transformers", "Transformers"),
        ("datasets", "Datasets"),
        ("faiss", "FAISS"),
        ("sentence_transformers", "SentenceTransformers"),
        ("jieba", "Jieba"),
        ("opencc", "OpenCC"),
        ("streamlit", "Streamlit"),
        ("loguru", "Loguru")
    ]
    
    missing_deps = []
    for dep, name in dependencies:
        try:
            __import__(dep)
            print(f"✓ {name} available")
        except ImportError:
            missing_deps.append(name)
            print(f"✗ {name} not available")
    
    if missing_deps:
        print(f"\nMissing dependencies: {missing_deps}")
        print("Please install missing dependencies with:")
        print("pip install -r requirements.txt")
        return False
    
    return True


def main():
    """Main test function / 主测试函数"""
    print("=" * 60)
    print("GRAG SYSTEM SETUP TEST")
    print("=" * 60)
    
    tests = [
        ("Dependencies", test_dependencies),
        ("Directories", test_directories),
        ("Imports", test_imports),
        ("Configuration", test_config),
        ("Logger", test_logger),
        ("Text Processing", test_text_processing),
        ("Environment", test_environment)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} PASSED")
            else:
                print(f"✗ {test_name} FAILED")
        except Exception as e:
            print(f"✗ {test_name} FAILED with error: {e}")
    
    print("\n" + "=" * 60)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All tests passed! System is ready.")
        print("\nNext steps:")
        print("1. Set up environment variables in .env file")
        print("2. Run: python scripts/download_data.py")
        print("3. Run: python scripts/train_sft.py")
        print("4. Run: python scripts/train_grpo.py")
        print("5. Run: streamlit run src/frontend/app.py")
    else:
        print("❌ Some tests failed. Please fix the issues before proceeding.")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
