# -*- coding: utf-8 -*-
"""
Helper Functions Module / 辅助函数模块

This module contains utility functions used throughout the GRAG system.
该模块包含GRAG系统中使用的实用函数。
"""

import re
import json
import torch
import random
import numpy as np
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
import jieba
import opencc
from .logger import get_logger

logger = get_logger(__name__)


def set_seed(seed: int = 3407):
    """
    Set random seed for reproducibility / 设置随机种子以确保可重现性
    
    Args:
        seed: Random seed / 随机种子
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    logger.info(f"Random seed set to {seed}")


def get_device() -> torch.device:
    """
    Get the best available device / 获取最佳可用设备
    
    Returns:
        PyTorch device / PyTorch设备
    """
    if torch.cuda.is_available():
        device = torch.device("cuda")
        logger.info(f"Using CUDA device: {torch.cuda.get_device_name()}")
    elif torch.backends.mps.is_available():
        device = torch.device("mps")
        logger.info("Using MPS device")
    else:
        device = torch.device("cpu")
        logger.info("Using CPU device")
    
    return device


def clean_text(text: str) -> str:
    """
    Clean and normalize Chinese text / 清理和标准化中文文本
    
    Args:
        text: Input text / 输入文本
        
    Returns:
        Cleaned text / 清理后的文本
    """
    if not text:
        return ""
    
    # Convert traditional Chinese to simplified / 繁体转简体
    converter = opencc.OpenCC('t2s')
    text = converter.convert(text)
    
    # Remove extra whitespace / 移除多余空白
    text = re.sub(r'\s+', ' ', text)
    
    # Remove special characters but keep Chinese punctuation / 移除特殊字符但保留中文标点
    text = re.sub(r'[^\u4e00-\u9fff\u3000-\u303f\uff00-\uffef\w\s.,!?;:()（）【】「」《》""''。，！？；：]', '', text)
    
    # Strip leading/trailing whitespace / 去除首尾空白
    text = text.strip()
    
    return text


def segment_chinese_text(text: str) -> List[str]:
    """
    Segment Chinese text using jieba / 使用jieba分词中文文本
    
    Args:
        text: Input Chinese text / 输入中文文本
        
    Returns:
        List of segmented words / 分词结果列表
    """
    return list(jieba.cut(text))


def calculate_text_similarity(text1: str, text2: str) -> float:
    """
    Calculate text similarity using simple word overlap / 使用简单词重叠计算文本相似度
    
    Args:
        text1: First text / 第一个文本
        text2: Second text / 第二个文本
        
    Returns:
        Similarity score between 0 and 1 / 0到1之间的相似度分数
    """
    words1 = set(segment_chinese_text(text1))
    words2 = set(segment_chinese_text(text2))
    
    if not words1 or not words2:
        return 0.0
    
    intersection = words1.intersection(words2)
    union = words1.union(words2)
    
    return len(intersection) / len(union) if union else 0.0


def extract_medical_entities(text: str) -> Dict[str, List[str]]:
    """
    Extract medical entities from text / 从文本中提取医疗实体
    
    Args:
        text: Input medical text / 输入医疗文本
        
    Returns:
        Dictionary of entity types and their values / 实体类型及其值的字典
    """
    entities = {
        "symptoms": [],      # 症状
        "diseases": [],      # 疾病
        "medications": [],   # 药物
        "body_parts": [],    # 身体部位
        "treatments": []     # 治疗方法
    }
    
    # Simple pattern matching for medical entities / 医疗实体的简单模式匹配
    # This is a basic implementation - in practice, you'd use a medical NER model
    # 这是一个基本实现 - 实际中应该使用医疗NER模型
    
    symptom_patterns = [
        r'(头痛|发热|咳嗽|腹痛|胸痛|呼吸困难|恶心|呕吐|腹泻|便秘|失眠|疲劳)',
        r'(疼痛|不适|肿胀|红肿|瘙痒|麻木|眩晕|心悸|气短)'
    ]
    
    disease_patterns = [
        r'(感冒|发烧|肺炎|胃炎|高血压|糖尿病|心脏病|肝炎|肾炎)',
        r'(癌症|肿瘤|结石|骨折|扭伤|过敏|哮喘|支气管炎)'
    ]
    
    for pattern in symptom_patterns:
        matches = re.findall(pattern, text)
        entities["symptoms"].extend(matches)
    
    for pattern in disease_patterns:
        matches = re.findall(pattern, text)
        entities["diseases"].extend(matches)
    
    # Remove duplicates / 去重
    for key in entities:
        entities[key] = list(set(entities[key]))
    
    return entities


def format_medical_response(response: str) -> str:
    """
    Format medical response with proper structure / 格式化医疗回复的结构
    
    Args:
        response: Raw response text / 原始回复文本
        
    Returns:
        Formatted response / 格式化的回复
    """
    # Add medical disclaimer if not present / 如果没有医疗免责声明则添加
    disclaimer = "\n\n⚠️ 免责声明：以上建议仅供参考，不能替代专业医疗诊断。如症状持续或加重，请及时就医。"
    
    if "免责声明" not in response and "仅供参考" not in response:
        response += disclaimer
    
    return response


def validate_medical_safety(text: str) -> Dict[str, Any]:
    """
    Validate medical safety of generated text / 验证生成文本的医疗安全性
    
    Args:
        text: Generated medical text / 生成的医疗文本
        
    Returns:
        Safety validation results / 安全验证结果
    """
    safety_check = {
        "is_safe": True,
        "warnings": [],
        "score": 1.0
    }
    
    # Check for harmful patterns / 检查有害模式
    harmful_patterns = [
        r'(一定|肯定|确诊|绝对)',  # Absolute statements / 绝对性陈述
        r'(不用看医生|不需要就医)',  # Discouraging medical consultation / 不鼓励就医
        r'(自己治疗|在家治疗)',     # Self-treatment advice / 自我治疗建议
    ]
    
    for pattern in harmful_patterns:
        if re.search(pattern, text):
            safety_check["is_safe"] = False
            safety_check["warnings"].append(f"Found potentially harmful pattern: {pattern}")
            safety_check["score"] -= 0.3
    
    # Check for medical disclaimer / 检查医疗免责声明
    if "免责声明" not in text and "仅供参考" not in text:
        safety_check["warnings"].append("Missing medical disclaimer")
        safety_check["score"] -= 0.2
    
    safety_check["score"] = max(0.0, safety_check["score"])
    
    return safety_check


def save_json(data: Any, filepath: Union[str, Path], ensure_ascii: bool = False):
    """
    Save data to JSON file / 保存数据到JSON文件
    
    Args:
        data: Data to save / 要保存的数据
        filepath: Output file path / 输出文件路径
        ensure_ascii: Whether to ensure ASCII encoding / 是否确保ASCII编码
    """
    filepath = Path(filepath)
    filepath.parent.mkdir(parents=True, exist_ok=True)
    
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=ensure_ascii, indent=2)
    
    logger.info(f"Data saved to {filepath}")


def load_json(filepath: Union[str, Path]) -> Any:
    """
    Load data from JSON file / 从JSON文件加载数据
    
    Args:
        filepath: Input file path / 输入文件路径
        
    Returns:
        Loaded data / 加载的数据
    """
    filepath = Path(filepath)
    
    if not filepath.exists():
        raise FileNotFoundError(f"File not found: {filepath}")
    
    with open(filepath, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    logger.info(f"Data loaded from {filepath}")
    return data


def truncate_text(text: str, max_length: int = 512, suffix: str = "...") -> str:
    """
    Truncate text to maximum length / 截断文本到最大长度
    
    Args:
        text: Input text / 输入文本
        max_length: Maximum length / 最大长度
        suffix: Suffix to add when truncated / 截断时添加的后缀
        
    Returns:
        Truncated text / 截断的文本
    """
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def batch_process(items: List[Any], batch_size: int = 32):
    """
    Process items in batches / 批量处理项目
    
    Args:
        items: List of items to process / 要处理的项目列表
        batch_size: Batch size / 批次大小
        
    Yields:
        Batches of items / 项目批次
    """
    for i in range(0, len(items), batch_size):
        yield items[i:i + batch_size]
