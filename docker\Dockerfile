# GRAG System Docker Image / GRAG系统Docker镜像
# Multi-stage build for optimized production image
# 多阶段构建以优化生产镜像

# Build stage / 构建阶段
FROM python:3.10-slim as builder

# Set environment variables / 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies / 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create virtual environment / 创建虚拟环境
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy requirements and install Python dependencies / 复制需求文件并安装Python依赖
COPY ../requirements.txt .
RUN pip install --upgrade pip && \
    pip install -r requirements.txt

# Production stage / 生产阶段
FROM python:3.10-slim as production

# Set environment variables / 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/opt/venv/bin:$PATH" \
    STREAMLIT_SERVER_PORT=8501 \
    STREAMLIT_SERVER_ADDRESS=0.0.0.0

# Install runtime dependencies / 安装运行时依赖
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy virtual environment from builder / 从构建器复制虚拟环境
COPY --from=builder /opt/venv /opt/venv

# Create app user / 创建应用用户
RUN useradd --create-home --shell /bin/bash app

# Set working directory / 设置工作目录
WORKDIR /app

# Copy application code / 复制应用代码
COPY --chown=app:app ../ .

# Create necessary directories / 创建必要目录
RUN mkdir -p data/processed data/embeddings models logs cache && \
    chown -R app:app /app

# Switch to app user / 切换到应用用户
USER app

# Expose port / 暴露端口
EXPOSE 8501

# Health check / 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8501/_stcore/health || exit 1

# Default command / 默认命令
CMD ["streamlit", "run", "src/frontend/app.py", "--server.port=8501", "--server.address=0.0.0.0"]

# Development stage / 开发阶段
FROM production as development

# Switch back to root for development tools / 切换回root以安装开发工具
USER root

# Install development dependencies / 安装开发依赖
RUN apt-get update && apt-get install -y \
    vim \
    htop \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install development Python packages / 安装开发Python包
RUN pip install \
    jupyter \
    ipython \
    pytest \
    black \
    flake8 \
    mypy

# Switch back to app user / 切换回应用用户
USER app

# Development command / 开发命令
CMD ["bash"]
