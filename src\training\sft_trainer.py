# -*- coding: utf-8 -*-
"""
Supervised Fine-Tuning (SFT) Trainer / 监督微调训练器

This module implements SFT training for the medical RAG system using Unsloth.
该模块使用Unsloth实现医疗RAG系统的SFT训练。
"""

import os
import torch
from typing import Dict, List, Any, Optional
from pathlib import Path
from datasets import Dataset
from transformers import TrainingArguments
from trl import SFTTrainer, SFTConfig
from unsloth import FastLanguageModel
import wandb

from ..utils import get_logger, config_manager, set_seed
from ..data_processing import MedicalDatasetLoader

logger = get_logger(__name__)


class SFTTrainer:
    """Supervised Fine-Tuning trainer / 监督微调训练器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize SFT trainer / 初始化SFT训练器
        
        Args:
            config: Training configuration / 训练配置
        """
        self.config = config or config_manager.training.sft
        self.model_config = config_manager.model
        self.model = None
        self.tokenizer = None
        self.trainer = None
        
        # Set random seed / 设置随机种子
        set_seed(config_manager.environment.seed)
        
        logger.info("Initialized SFT trainer")
    
    def load_model(self):
        """Load and prepare model for training / 加载并准备训练模型"""
        logger.info(f"Loading model: {self.model_config.base_model}")
        
        # Load model with Unsloth / 使用Unsloth加载模型
        self.model, self.tokenizer = FastLanguageModel.from_pretrained(
            model_name=self.model_config.base_model,
            max_seq_length=self.model_config.max_length,
            load_in_4bit=self.model_config.load_in_4bit,
            dtype=None,  # Auto detection
        )
        
        # Apply LoRA / 应用LoRA
        self.model = FastLanguageModel.get_peft_model(
            self.model,
            r=self.model_config.lora_config["r"],
            target_modules=self.model_config.lora_config["target_modules"],
            lora_alpha=self.model_config.lora_config["lora_alpha"],
            lora_dropout=self.model_config.lora_config["lora_dropout"],
            bias=self.model_config.lora_config["bias"],
            use_gradient_checkpointing="unsloth",
            random_state=config_manager.environment.seed,
        )
        
        # Set chat template / 设置聊天模板
        self._setup_chat_template()
        
        logger.info("Model loaded and configured successfully")
    
    def _setup_chat_template(self):
        """Setup chat template for medical conversations / 设置医疗对话模板"""
        chat_template = """{% for message in messages %}
{% if message['role'] == 'system' %}
{{ message['content'] + eos_token }}
{% elif message['role'] == 'user' %}
{{ '用户：' + message['content'] + '\n' }}
{% elif message['role'] == 'assistant' %}
{{ '医生：' + message['content'] + eos_token }}
{% endif %}
{% endfor %}"""
        
        self.tokenizer.chat_template = chat_template
        logger.info("Chat template configured")
    
    def prepare_dataset(self, data_path: str = None) -> Dataset:
        """
        Prepare training dataset / 准备训练数据集
        
        Args:
            data_path: Path to training data / 训练数据路径
            
        Returns:
            Prepared dataset / 准备好的数据集
        """
        logger.info("Preparing training dataset...")
        
        if data_path and Path(data_path).exists():
            # Load from file / 从文件加载
            loader = MedicalDatasetLoader()
            train_data = loader.load_processed_data("train", Path(data_path).parent)
        else:
            # Load and process from scratch / 从头加载和处理
            loader = MedicalDatasetLoader()
            raw_dataset = loader.load_raw_dataset()
            processed_data = loader.preprocess_data(raw_dataset)
            train_data, _, _ = loader.split_dataset(processed_data)
            train_data = loader.prepare_sft_data(train_data)
        
        # Convert to HF Dataset / 转换为HF数据集
        def format_messages(example):
            """Format messages for training / 格式化训练消息"""
            messages = example["messages"]
            text = self.tokenizer.apply_chat_template(
                messages, 
                tokenize=False, 
                add_generation_prompt=False
            )
            return {"text": text}
        
        dataset = Dataset.from_list(train_data)
        dataset = dataset.map(format_messages)
        
        logger.info(f"Prepared dataset with {len(dataset)} samples")
        return dataset
    
    def train(self, train_dataset: Dataset, eval_dataset: Optional[Dataset] = None):
        """
        Start SFT training / 开始SFT训练
        
        Args:
            train_dataset: Training dataset / 训练数据集
            eval_dataset: Evaluation dataset / 评估数据集
        """
        logger.info("Starting SFT training...")
        
        if self.model is None:
            self.load_model()
        
        # Setup training arguments / 设置训练参数
        training_args = SFTConfig(
            output_dir=self.config["output_dir"],
            num_train_epochs=self.config["num_train_epochs"],
            per_device_train_batch_size=self.config["per_device_train_batch_size"],
            per_device_eval_batch_size=self.config.get("per_device_eval_batch_size", 2),
            gradient_accumulation_steps=self.config["gradient_accumulation_steps"],
            learning_rate=self.config["learning_rate"],
            warmup_ratio=self.config["warmup_ratio"],
            logging_steps=self.config["logging_steps"],
            save_steps=self.config["save_steps"],
            eval_steps=self.config.get("eval_steps", 500),
            max_seq_length=self.config["max_length"],
            optim=self.config["optim"],
            weight_decay=self.config["weight_decay"],
            lr_scheduler_type=self.config["lr_scheduler_type"],
            seed=config_manager.environment.seed,
            dataset_text_field="text",
            report_to="wandb" if wandb.run else "none",
            evaluation_strategy="steps" if eval_dataset else "no",
            save_strategy="steps",
            load_best_model_at_end=True if eval_dataset else False,
            metric_for_best_model="eval_loss" if eval_dataset else None,
            greater_is_better=False,
        )
        
        # Initialize trainer / 初始化训练器
        self.trainer = SFTTrainer(
            model=self.model,
            tokenizer=self.tokenizer,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            args=training_args,
        )
        
        # Start training / 开始训练
        self.trainer.train()
        
        logger.info("SFT training completed")
    
    def save_model(self, output_dir: str = None):
        """
        Save trained model / 保存训练好的模型
        
        Args:
            output_dir: Output directory / 输出目录
        """
        output_dir = output_dir or self.config["output_dir"]
        
        logger.info(f"Saving model to {output_dir}")
        
        # Save with Unsloth / 使用Unsloth保存
        self.model.save_pretrained(output_dir)
        self.tokenizer.save_pretrained(output_dir)
        
        # Also save as merged model for inference / 同时保存合并模型用于推理
        merged_dir = Path(output_dir) / "merged"
        self.model.save_pretrained_merged(
            str(merged_dir), 
            self.tokenizer, 
            save_method="merged_16bit"
        )
        
        logger.info("Model saved successfully")
    
    def evaluate(self, eval_dataset: Dataset) -> Dict[str, float]:
        """
        Evaluate model / 评估模型
        
        Args:
            eval_dataset: Evaluation dataset / 评估数据集
            
        Returns:
            Evaluation metrics / 评估指标
        """
        if self.trainer is None:
            raise ValueError("Trainer not initialized. Call train() first.")
        
        logger.info("Evaluating model...")
        
        eval_results = self.trainer.evaluate(eval_dataset)
        
        logger.info(f"Evaluation results: {eval_results}")
        return eval_results


def main():
    """Test SFT trainer / 测试SFT训练器"""
    try:
        # Initialize trainer / 初始化训练器
        trainer = SFTTrainer()
        
        # Load model / 加载模型
        trainer.load_model()
        
        # Prepare dataset / 准备数据集
        train_dataset = trainer.prepare_dataset()
        
        # Start training / 开始训练
        trainer.train(train_dataset)
        
        # Save model / 保存模型
        trainer.save_model()
        
        logger.info("SFT training completed successfully!")
        
    except Exception as e:
        logger.error(f"Error in SFT training: {e}")
        raise


if __name__ == "__main__":
    main()
