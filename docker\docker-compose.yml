# GRAG System Docker Compose / GRAG系统Docker Compose
# Orchestrates the complete GRAG system with all services
# 编排完整的GRAG系统及其所有服务

version: '3.8'

services:
  # Main GRAG application / 主要GRAG应用
  grag-app:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: production
    container_name: grag-app
    ports:
      - "8501:8501"
    volumes:
      - ../data:/app/data
      - ../models:/app/models
      - ../logs:/app/logs
      - ../cache:/app/cache
    environment:
      - STREAMLIT_SERVER_PORT=8501
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0
      - LOG_LEVEL=INFO
      - CUDA_VISIBLE_DEVICES=${CUDA_VISIBLE_DEVICES:-}
    env_file:
      - ../.env
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - grag-network

  # Development environment / 开发环境
  grag-dev:
    build:
      context: ..
      dockerfile: docker/Dockerfile
      target: development
    container_name: grag-dev
    ports:
      - "8502:8501"  # Different port for dev
      - "8888:8888"  # Jupyter
    volumes:
      - ..:/app
      - ../data:/app/data
      - ../models:/app/models
      - ../logs:/app/logs
    environment:
      - STREAMLIT_SERVER_PORT=8501
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0
      - JUPYTER_ENABLE_LAB=yes
    env_file:
      - ../.env
    command: bash -c "jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root --NotebookApp.token='' --NotebookApp.password='' & streamlit run src/frontend/app.py --server.port=8501 --server.address=0.0.0.0 & wait"
    networks:
      - grag-network
    profiles:
      - dev

  # Vector database (optional external) / 向量数据库（可选外部）
  # Uncomment if you want to use external vector database
  # 如果要使用外部向量数据库请取消注释
  # vector-db:
  #   image: qdrant/qdrant:latest
  #   container_name: grag-vector-db
  #   ports:
  #     - "6333:6333"
  #   volumes:
  #     - vector_data:/qdrant/storage
  #   environment:
  #     - QDRANT__SERVICE__HTTP_PORT=6333
  #   networks:
  #     - grag-network
  #   profiles:
  #     - external-db

  # Monitoring with Prometheus (optional) / 使用Prometheus监控（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: grag-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ../monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - grag-network
    profiles:
      - monitoring

  # Grafana for visualization (optional) / Grafana可视化（可选）
  grafana:
    image: grafana/grafana:latest
    container_name: grag-grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ../monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ../monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - grag-network
    profiles:
      - monitoring

  # Redis for caching (optional) / Redis缓存（可选）
  redis:
    image: redis:7-alpine
    container_name: grag-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - grag-network
    profiles:
      - cache

  # Nginx reverse proxy (optional) / Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: grag-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ../nginx/nginx.conf:/etc/nginx/nginx.conf
      - ../nginx/ssl:/etc/nginx/ssl
    depends_on:
      - grag-app
    networks:
      - grag-network
    profiles:
      - proxy

# Networks / 网络
networks:
  grag-network:
    driver: bridge

# Volumes / 卷
volumes:
  vector_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  redis_data:
    driver: local

# Additional compose files for different environments
# 不同环境的额外compose文件

# Development override / 开发覆盖
# Use: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
# 使用: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
