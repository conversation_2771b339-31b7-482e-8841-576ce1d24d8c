# -*- coding: utf-8 -*-
"""
Retriever Module / 检索器模块

This module implements intelligent retrieval with self-correction capabilities.
该模块实现具有自我修正能力的智能检索。
"""

import re
from typing import List, Dict, Tuple, Optional, Any
from .enhanced_vector_store import MedicalVectorStore
from ..utils import get_logger, get_config_manager, extract_medical_entities

logger = get_logger(__name__)


class MedicalRetriever:
    """Medical document retriever with self-correction / 具有自我修正功能的医疗文档检索器"""
    
    def __init__(self, vector_store: MedicalVectorStore = None):
        """
        Initialize medical retriever / 初始化医疗检索器
        
        Args:
            vector_store: Vector store instance / 向量存储实例
        """
        self.vector_store = vector_store
        config_manager = get_config_manager()
        self.retrieval_config = config_manager.retrieval
        self.search_config = self.retrieval_config.search
        self.correction_config = self.retrieval_config.self_correction
        
        logger.info("Medical retriever initialized")
    
    def retrieve(self, query: str, top_k: int = None, 
                similarity_threshold: float = None,
                enable_self_correction: bool = None) -> List[Dict[str, Any]]:
        """
        Retrieve relevant documents with optional self-correction / 检索相关文档并可选自我修正
        
        Args:
            query: Search query / 搜索查询
            top_k: Number of documents to retrieve / 要检索的文档数量
            similarity_threshold: Minimum similarity threshold / 最小相似度阈值
            enable_self_correction: Whether to enable self-correction / 是否启用自我修正
            
        Returns:
            List of retrieved documents / 检索到的文档列表
        """
        if self.vector_store is None:
            raise ValueError("Vector store not initialized")
        
        # Get default values / 获取默认值
        top_k = top_k or self.search_config.get("top_k", 10)
        similarity_threshold = similarity_threshold or self.search_config.get("similarity_threshold", 0.7)
        enable_self_correction = enable_self_correction if enable_self_correction is not None else self.correction_config.get("enabled", True)
        
        logger.info(f"Retrieving documents for query: {query[:50]}...")
        
        # Initial retrieval / 初始检索
        results = self._basic_retrieve(query, top_k, similarity_threshold)
        
        # Apply self-correction if enabled and results are insufficient / 如果启用且结果不足则应用自我修正
        if enable_self_correction and self._should_apply_correction(results, query):
            logger.info("Applying self-correction to improve retrieval")
            results = self._apply_self_correction(query, results, top_k, similarity_threshold)
        
        # Post-process results / 后处理结果
        processed_results = self._post_process_results(results, query)
        
        logger.info(f"Retrieved {len(processed_results)} documents")
        return processed_results
    
    def _basic_retrieve(self, query: str, top_k: int, 
                       similarity_threshold: float) -> List[Tuple[Dict, float]]:
        """
        Perform basic retrieval / 执行基本检索
        
        Args:
            query: Search query / 搜索查询
            top_k: Number of documents to retrieve / 要检索的文档数量
            similarity_threshold: Minimum similarity threshold / 最小相似度阈值
            
        Returns:
            List of (document, score) tuples / (文档, 分数)元组列表
        """
        return self.vector_store.search(query, top_k, similarity_threshold)
    
    def _should_apply_correction(self, results: List[Tuple[Dict, float]], 
                               query: str) -> bool:
        """
        Determine if self-correction should be applied / 判断是否应该应用自我修正
        
        Args:
            results: Initial retrieval results / 初始检索结果
            query: Original query / 原始查询
            
        Returns:
            Whether to apply correction / 是否应用修正
        """
        if not self.correction_config.get("enabled", True):
            return False
        
        # Check if results are insufficient / 检查结果是否不足
        min_results = 3  # Minimum expected results / 最少期望结果数
        if len(results) < min_results:
            logger.debug("Insufficient results, applying correction")
            return True
        
        # Check if top result score is too low / 检查顶部结果分数是否过低
        confidence_threshold = self.correction_config.get("confidence_threshold", 0.8)
        if results and results[0][1] < confidence_threshold:
            logger.debug("Low confidence in top result, applying correction")
            return True
        
        return False
    
    def _apply_self_correction(self, original_query: str, 
                             initial_results: List[Tuple[Dict, float]],
                             top_k: int, similarity_threshold: float) -> List[Tuple[Dict, float]]:
        """
        Apply self-correction strategies / 应用自我修正策略
        
        Args:
            original_query: Original search query / 原始搜索查询
            initial_results: Initial retrieval results / 初始检索结果
            top_k: Number of documents to retrieve / 要检索的文档数量
            similarity_threshold: Minimum similarity threshold / 最小相似度阈值
            
        Returns:
            Corrected retrieval results / 修正后的检索结果
        """
        strategies = self.correction_config.get("strategies", ["query_expansion", "parameter_adjustment"])
        max_iterations = self.correction_config.get("max_iterations", 3)
        
        best_results = initial_results
        current_query = original_query
        
        for iteration in range(max_iterations):
            logger.debug(f"Self-correction iteration {iteration + 1}")
            
            for strategy in strategies:
                if strategy == "query_expansion":
                    expanded_query = self._expand_query(current_query)
                    if expanded_query != current_query:
                        results = self._basic_retrieve(expanded_query, top_k, similarity_threshold)
                        if self._is_better_results(results, best_results):
                            best_results = results
                            current_query = expanded_query
                            logger.debug(f"Query expansion improved results: {expanded_query}")
                
                elif strategy == "parameter_adjustment":
                    # Try with lower threshold / 尝试更低的阈值
                    lower_threshold = max(0.5, similarity_threshold - 0.1)
                    results = self._basic_retrieve(current_query, top_k * 2, lower_threshold)
                    if self._is_better_results(results, best_results):
                        best_results = results
                        logger.debug("Parameter adjustment improved results")
            
            # Check if we have sufficient results / 检查是否有足够的结果
            if len(best_results) >= 3 and best_results[0][1] >= self.correction_config.get("confidence_threshold", 0.8):
                break
        
        return best_results
    
    def _expand_query(self, query: str) -> str:
        """
        Expand query with medical synonyms and related terms / 使用医疗同义词和相关术语扩展查询
        
        Args:
            query: Original query / 原始查询
            
        Returns:
            Expanded query / 扩展后的查询
        """
        # Extract medical entities / 提取医疗实体
        entities = extract_medical_entities(query)
        
        # Medical synonym mapping / 医疗同义词映射
        synonyms = {
            "头痛": ["头疼", "脑袋疼", "头部疼痛"],
            "发热": ["发烧", "体温升高"],
            "咳嗽": ["咳", "干咳"],
            "腹痛": ["肚子疼", "胃疼"],
            "胸痛": ["胸闷", "心口疼"],
            "呼吸困难": ["气喘", "喘不过气"],
        }
        
        expanded_terms = []
        
        # Add synonyms for detected symptoms / 为检测到的症状添加同义词
        for symptom in entities.get("symptoms", []):
            if symptom in synonyms:
                expanded_terms.extend(synonyms[symptom][:2])  # Add top 2 synonyms
        
        # Create expanded query / 创建扩展查询
        if expanded_terms:
            expanded_query = query + " " + " ".join(expanded_terms)
            return expanded_query
        
        return query
    
    def _is_better_results(self, new_results: List[Tuple[Dict, float]], 
                          current_results: List[Tuple[Dict, float]]) -> bool:
        """
        Check if new results are better than current results / 检查新结果是否比当前结果更好
        
        Args:
            new_results: New retrieval results / 新检索结果
            current_results: Current best results / 当前最佳结果
            
        Returns:
            Whether new results are better / 新结果是否更好
        """
        if not current_results:
            return len(new_results) > 0
        
        if not new_results:
            return False
        
        # Compare by number of results and top score / 通过结果数量和顶部分数比较
        if len(new_results) > len(current_results):
            return True
        
        if len(new_results) == len(current_results) and new_results[0][1] > current_results[0][1]:
            return True
        
        return False
    
    def _post_process_results(self, results: List[Tuple[Dict, float]], 
                            query: str) -> List[Dict[str, Any]]:
        """
        Post-process retrieval results / 后处理检索结果
        
        Args:
            results: Raw retrieval results / 原始检索结果
            query: Original query / 原始查询
            
        Returns:
            Processed results / 处理后的结果
        """
        processed_results = []
        
        for doc, score in results:
            processed_doc = {
                "id": doc.get("id"),
                "query": doc.get("query", ""),
                "response": doc.get("response", ""),
                "text": doc.get("text", ""),
                "score": score,
                "metadata": doc.get("metadata", {}),
                "medical_entities": doc.get("medical_entities", {}),
                "relevance_explanation": self._explain_relevance(doc, query, score)
            }
            processed_results.append(processed_doc)
        
        return processed_results
    
    def _explain_relevance(self, doc: Dict, query: str, score: float) -> str:
        """
        Explain why a document is relevant / 解释文档为什么相关
        
        Args:
            doc: Retrieved document / 检索到的文档
            query: Original query / 原始查询
            score: Similarity score / 相似度分数
            
        Returns:
            Relevance explanation / 相关性解释
        """
        doc_text = doc.get("text", "").lower()
        query_lower = query.lower()
        
        # Find common terms / 找到共同术语
        query_terms = set(query_lower.split())
        doc_terms = set(doc_text.split())
        common_terms = query_terms.intersection(doc_terms)
        
        if common_terms:
            return f"匹配关键词: {', '.join(list(common_terms)[:3])}, 相似度: {score:.3f}"
        else:
            return f"语义相似度: {score:.3f}"
    
    def get_retrieval_stats(self) -> Dict[str, Any]:
        """
        Get retrieval statistics / 获取检索统计信息
        
        Returns:
            Retrieval statistics / 检索统计信息
        """
        if self.vector_store is None:
            return {"error": "Vector store not initialized"}
        
        return {
            "total_documents": len(self.vector_store.documents),
            "embedding_model": self.vector_store.embedding_model_name,
            "vector_dimension": self.vector_store.dimension,
            "self_correction_enabled": self.correction_config.get("enabled", True)
        }
