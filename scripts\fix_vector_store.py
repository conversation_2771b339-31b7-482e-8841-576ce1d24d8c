#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vector Store Fix Script / 向量存储修复脚本

This script fixes the hanging issue in vector store creation by using smaller batches
and better memory management.
该脚本通过使用更小的批次和更好的内存管理来修复向量存储创建中的挂起问题。
"""

import sys
import os
import gc
import time
from pathlib import Path

# Add project root and src to path
project_root = Path(__file__).parent.parent
src_path = project_root / "src"

# Change to project root directory
os.chdir(project_root)

# Add both paths to sys.path
for path in [str(project_root), str(src_path)]:
    if path not in sys.path:
        sys.path.insert(0, path)

# Set PYTHONPATH environment variable
os.environ['PYTHONPATH'] = str(src_path) + os.pathsep + os.environ.get('PYTHONPATH', '')

try:
    from src.retrieval import MedicalVectorStore, create_vector_store_from_knowledge_base
    from src.utils import get_logger, get_config_manager
except ImportError:
    from retrieval import MedicalVectorStore, create_vector_store_from_knowledge_base
    from utils import get_logger, get_config_manager

logger = get_logger(__name__)


def create_vector_store_with_progress(
    knowledge_base_path: str, 
    vector_store_path: str,
    embedding_model: str = None,
    small_batch_size: int = 8  # Much smaller batch size
):
    """
    Create vector store with progress tracking and better memory management
    创建带有进度跟踪和更好内存管理的向量存储
    """
    import json
    
    logger.info("Starting optimized vector store creation...")
    
    # Load knowledge base / 加载知识库
    documents = []
    logger.info(f"Loading knowledge base from {knowledge_base_path}")
    
    try:
        with open(knowledge_base_path, "r", encoding="utf-8") as f:
            for line_num, line in enumerate(f, 1):
                if line.strip():
                    try:
                        doc = json.loads(line.strip())
                        documents.append(doc)
                    except json.JSONDecodeError as e:
                        logger.warning(f"Skipping invalid JSON on line {line_num}: {e}")
                        
        logger.info(f"Loaded {len(documents)} documents from knowledge base")
        
    except FileNotFoundError:
        logger.error(f"Knowledge base file not found: {knowledge_base_path}")
        return None
    except Exception as e:
        logger.error(f"Error loading knowledge base: {e}")
        return None
    
    if not documents:
        logger.error("No valid documents found in knowledge base")
        return None
    
    # Create vector store with smaller batches / 使用更小的批次创建向量存储
    logger.info("Creating vector store with optimized settings...")
    vector_store = MedicalVectorStore(embedding_model=embedding_model)
    
    # Override batch size for this operation / 为此操作覆盖批次大小
    original_batch_size = vector_store.batch_size
    vector_store.batch_size = small_batch_size
    
    try:
        # Process documents in very small batches with progress tracking
        # 使用非常小的批次处理文档并跟踪进度
        total_docs = len(documents)
        processed = 0
        
        logger.info(f"Processing {total_docs} documents in batches of {small_batch_size}")
        
        # Load embedding model first / 首先加载嵌入模型
        vector_store.load_embedding_model()
        
        # Process in small chunks / 小块处理
        for i in range(0, total_docs, small_batch_size):
            batch = documents[i:i + small_batch_size]
            batch_num = (i // small_batch_size) + 1
            total_batches = (total_docs + small_batch_size - 1) // small_batch_size
            
            logger.info(f"Processing batch {batch_num}/{total_batches} ({len(batch)} documents)")
            
            try:
                # Extract texts for this batch / 为此批次提取文本
                texts = []
                for doc in batch:
                    text = doc.get("text", "")
                    if not text:
                        # Create text from query and response if text is missing
                        # 如果缺少文本，则从查询和回复创建文本
                        query = doc.get("query", "")
                        response = doc.get("response", "")
                        text = f"问题：{query}\n回答：{response}" if query or response else "空文档"
                    texts.append(text)
                
                # Generate embeddings for this batch / 为此批次生成嵌入
                logger.debug(f"Generating embeddings for batch {batch_num}...")
                embeddings = vector_store.encode_texts(texts)
                
                # Create index on first batch / 在第一批时创建索引
                if vector_store.index is None:
                    actual_dimension = embeddings.shape[1]
                    if vector_store.dimension != actual_dimension:
                        logger.info(f"Updating dimension from {vector_store.dimension} to {actual_dimension}")
                        vector_store.dimension = actual_dimension
                    vector_store.create_index()
                
                # Add to FAISS index / 添加到FAISS索引
                logger.debug(f"Adding embeddings to FAISS index...")
                vector_store.index.add(embeddings)
                
                # Store documents and metadata / 存储文档和元数据
                vector_store.documents.extend(batch)
                vector_store.metadata.extend([doc.get("metadata", {}) for doc in batch])
                
                processed += len(batch)
                logger.info(f"Progress: {processed}/{total_docs} documents processed ({processed/total_docs*100:.1f}%)")
                
                # Force garbage collection / 强制垃圾回收
                del embeddings, texts
                gc.collect()
                
                # Small delay to prevent overwhelming the system / 小延迟以防止系统过载
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error processing batch {batch_num}: {e}")
                # Continue with next batch instead of failing completely
                # 继续下一批而不是完全失败
                continue
        
        # Save the vector store / 保存向量存储
        logger.info(f"Saving vector store to {vector_store_path}")
        vector_store.save(vector_store_path)
        
        logger.info(f"Successfully created vector store with {len(vector_store.documents)} documents")
        return vector_store
        
    except Exception as e:
        logger.error(f"Error creating vector store: {e}")
        return None
    finally:
        # Restore original batch size / 恢复原始批次大小
        vector_store.batch_size = original_batch_size


def main():
    """Main function to fix vector store creation / 修复向量存储创建的主函数"""
    try:
        logger.info("Starting vector store fix...")
        
        # Get configuration / 获取配置
        config_manager = get_config_manager()
        vector_store_path = config_manager.retrieval.vector_db.get("save_path", "data/embeddings/faiss_index")
        knowledge_base_path = "data/processed/knowledge_base.jsonl"
        
        # Check if knowledge base exists / 检查知识库是否存在
        if not Path(knowledge_base_path).exists():
            logger.error(f"Knowledge base not found: {knowledge_base_path}")
            logger.info("Please run the data download script first to create the knowledge base")
            return
        
        # Create vector store with optimized settings / 使用优化设置创建向量存储
        vector_store = create_vector_store_with_progress(
            knowledge_base_path=knowledge_base_path,
            vector_store_path=vector_store_path,
            small_batch_size=4  # Very small batch size to avoid hanging
        )
        
        if vector_store:
            # Test the vector store / 测试向量存储
            logger.info("Testing vector store...")
            test_queries = [
                "头痛怎么办",
                "发烧如何处理", 
                "咳嗽的原因"
            ]
            
            for query in test_queries:
                try:
                    results = vector_store.search(query, top_k=3)
                    logger.info(f"Query: {query} - Found {len(results)} results")
                    if results:
                        logger.info(f"Top result score: {results[0][1]:.4f}")
                except Exception as e:
                    logger.error(f"Error testing query '{query}': {e}")
            
            logger.info("=" * 60)
            logger.info("VECTOR STORE CREATION COMPLETED SUCCESSFULLY!")
            logger.info("=" * 60)
            logger.info(f"Vector store saved to: {vector_store_path}")
            logger.info(f"Total documents: {len(vector_store.documents)}")
            logger.info("=" * 60)
        else:
            logger.error("Failed to create vector store")
            
    except Exception as e:
        logger.error(f"Error in vector store fix: {e}")
        raise


if __name__ == "__main__":
    main()
