# GRAG Medical AI System - Execution Guide / 执行指南

## Overview / 概述

This guide provides step-by-step instructions for setting up and running the GRAG Medical AI system. All scripts are numbered for easy sequential execution.

本指南提供了设置和运行GRAG医疗AI系统的分步说明。所有脚本都已编号，便于按顺序执行。

## Prerequisites / 前提条件

1. **Python Environment / Python环境**: Python 3.8+ with virtual environment
2. **Dependencies / 依赖**: All packages installed via `pip install -r requirements.txt`
3. **Hardware / 硬件**: At least 8GB RAM recommended for vector store creation

## Execution Steps / 执行步骤

### Step 0: Test Setup (Optional) / 步骤0：测试设置（可选）

```bash
python scripts/00_test_setup.py
```

**Purpose / 目的**: Verify that all dependencies are properly installed and the environment is correctly configured.

**Expected Output / 预期输出**: 
- ✅ All dependency checks pass
- ✅ Environment configuration verified
- ✅ Basic functionality tests pass

---

### Step 1: Download and Process Data / 步骤1：下载和处理数据

```bash
python scripts/01_download_data.py
```

**Purpose / 目的**: 
- Download the medical consultation dataset
- Preprocess and clean the data
- Split into training, validation, and test sets
- Create knowledge base for retrieval

**Expected Output / 预期输出**:
```
Training samples: 800
Validation samples: 100
Test samples: 100
Knowledge base items: 900
```

**Generated Files / 生成的文件**:
- `data/processed/train.jsonl`
- `data/processed/val.jsonl`
- `data/processed/test.jsonl`
- `data/processed/knowledge_base.jsonl`
- `data/processed/sft/` (SFT training data)
- `data/processed/grpo/` (GRPO training data)

**Duration / 持续时间**: ~30 seconds

---

### Step 2: Create Vector Store / 步骤2：创建向量存储

```bash
python scripts/02_create_vector_store.py
```

**Purpose / 目的**: 
- Create FAISS vector index from knowledge base
- Generate embeddings for all documents
- Enable semantic search capabilities

**Expected Output / 预期输出**:
```
🔄 Batch 1/225 (0.4%) - Processing 4 documents
📈 Progress: 4/900 documents (0.4%)
⏱️ Elapsed: 2.1s | ETA: 472.4s
📊 [█░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░] 4/900
...
🎉 VECTOR STORE CREATION COMPLETED SUCCESSFULLY!
📊 Total documents processed: 900
💾 Saved to: data/embeddings/faiss_index
```

**Generated Files / 生成的文件**:
- `data/embeddings/faiss_index/` (FAISS vector store)

**Duration / 持续时间**: ~5-10 minutes (depending on hardware)

**Progress Features / 进度功能**:
- Real-time batch processing updates
- Progress bar visualization
- ETA calculation
- Memory usage optimization

---

### Step 3: Train SFT Model / 步骤3：训练SFT模型

```bash
python scripts/03_train_sft.py
```

**Purpose / 目的**: 
- Fine-tune the base language model using Supervised Fine-Tuning
- Adapt the model to medical consultation tasks
- Save the trained model for inference

**Expected Output / 预期输出**:
- Training progress with loss metrics
- Model checkpoints saved
- Final model evaluation results

**Generated Files / 生成的文件**:
- `models/sft/` (Trained SFT model)
- Training logs and metrics

**Duration / 持续时间**: Varies based on hardware and training configuration

---

### Step 4: Train GRPO Model / 步骤4：训练GRPO模型

```bash
python scripts/04_train_grpo.py
```

**Purpose / 目的**: 
- Apply Group Relative Policy Optimization
- Further improve model performance
- Align model outputs with human preferences

**Expected Output / 预期输出**:
- GRPO training progress
- Policy optimization metrics
- Final optimized model

**Generated Files / 生成的文件**:
- `models/grpo/` (GRPO optimized model)
- Training logs and metrics

**Duration / 持续时间**: Varies based on hardware and training configuration

---

### Step 5: Run Application / 步骤5：运行应用

```bash
python scripts/05_run_app.py
```

**Purpose / 目的**: 
- Launch the Streamlit web interface
- Provide interactive medical consultation
- Demonstrate the complete RAG system

**Expected Output / 预期输出**:
```
🏥 Starting GRAG Medical AI Application...
✅ Vector store loaded successfully
✅ Model loaded successfully
✅ RAG system initialized
🌐 Streamlit app starting...

  You can now view your Streamlit app in your browser.
  Local URL: http://localhost:8501
```

**Access / 访问**: Open browser and navigate to `http://localhost:8501`

---

### Step 99: Test Model (Optional) / 步骤99：测试模型（可选）

```bash
python scripts/99_test_model.py
```

**Purpose / 目的**: 
- Comprehensive system testing
- Performance benchmarking
- Quality assurance

## Troubleshooting / 故障排除

### Common Issues / 常见问题

1. **Import Errors / 导入错误**
   ```bash
   # Ensure you're in the project root directory
   cd /path/to/GRAG
   
   # Activate virtual environment
   source .venv/bin/activate  # Linux/Mac
   .venv\Scripts\activate     # Windows
   ```

2. **Memory Issues During Vector Store Creation / 向量存储创建时的内存问题**
   - Reduce batch size in `02_create_vector_store.py`
   - Close other applications to free memory
   - Consider using a machine with more RAM

3. **Model Loading Issues / 模型加载问题**
   - Check internet connection for model downloads
   - Verify sufficient disk space
   - Ensure CUDA is available if using GPU

4. **Port Already in Use / 端口已被占用**
   ```bash
   # Kill process using port 8501
   netstat -ano | findstr :8501  # Windows
   lsof -ti:8501 | xargs kill    # Linux/Mac
   ```

### Getting Help / 获取帮助

1. **Check Logs / 检查日志**: All scripts provide detailed logging
2. **Verify Files / 验证文件**: Ensure all generated files exist and are not empty
3. **System Requirements / 系统要求**: Verify hardware meets minimum requirements

## File Structure After Completion / 完成后的文件结构

```
GRAG/
├── data/
│   ├── processed/
│   │   ├── train.jsonl
│   │   ├── val.jsonl
│   │   ├── test.jsonl
│   │   ├── knowledge_base.jsonl
│   │   ├── sft/
│   │   └── grpo/
│   └── embeddings/
│       └── faiss_index/
├── models/
│   ├── sft/
│   └── grpo/
└── scripts/
    ├── 00_test_setup.py
    ├── 01_download_data.py
    ├── 02_create_vector_store.py
    ├── 03_train_sft.py
    ├── 04_train_grpo.py
    ├── 05_run_app.py
    └── 99_test_model.py
```

## Performance Tips / 性能提示

1. **Vector Store Creation / 向量存储创建**:
   - Use SSD storage for faster I/O
   - Ensure sufficient RAM (8GB+ recommended)
   - Close unnecessary applications

2. **Model Training / 模型训练**:
   - Use GPU if available
   - Monitor system resources
   - Adjust batch sizes based on available memory

3. **Application Runtime / 应用运行时**:
   - Keep vector store in memory for faster queries
   - Use caching for frequently accessed data
   - Monitor response times

## Next Steps / 下一步

After completing all steps:

1. **Customize / 自定义**: Modify configurations for your specific use case
2. **Extend / 扩展**: Add new features or integrate with other systems
3. **Deploy / 部署**: Consider deployment options for production use
4. **Monitor / 监控**: Set up logging and monitoring for production systems

---

**Note / 注意**: This guide assumes a development environment. For production deployment, additional considerations for security, scalability, and monitoring are required.

**Support / 支持**: For issues or questions, refer to the project documentation or create an issue in the repository.
