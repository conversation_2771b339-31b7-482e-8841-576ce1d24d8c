# GRAG Safe Requirements - 2025年6月最新版本
# 这个文件移除了有问题的依赖，可以安全安装

# Core ML/AI Libraries
torch>=2.0.0
transformers>=4.35.0
datasets>=2.14.0
accelerate>=0.24.0
peft>=0.6.0
trl>=0.7.0
# unsloth - 需要单独安装

# Lang<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> (2025 Latest)
langgraph>=0.2.0
langchain>=0.3.0
langchain-community>=0.3.0
langchain-openai>=0.2.0
langchain-anthropic>=0.2.0
langchain-text-splitters>=0.3.0
langchainhub>=0.1.15

# Vector Database and Search
faiss-cpu==1.10.0
sentence-transformers>=2.2.2
chromadb>=0.4.0

# Embedding Models (Chinese Medical)
FlagEmbedding>=1.2.0  # BGE embeddings

# Web Search and Scraping
tavily-python>=0.3.0
requests>=2.31.0
beautifulsoup4>=4.12.0

# Data Processing
pandas>=2.0.0
numpy>=1.24.0
scikit-learn>=1.3.0
jieba>=0.42.1
opencc-python-reimplemented>=0.1.7  # Chinese text processing

# Evaluation
rouge-score>=0.1.2
nltk>=3.8.0
bert-score>=0.3.13
ragas>=0.1.0  # RAG evaluation

# Frontend
streamlit>=1.28.0
plotly>=5.17.0
altair>=5.1.0
streamlit-chat>=0.1.1
streamlit-option-menu>=0.3.6

# Utilities
tqdm>=4.66.0
wandb>=0.16.0
python-dotenv>=1.0.0
pyyaml>=6.0.1
loguru>=0.7.2
rich>=13.0.0  # Better console output

# Medical NLP
scispacy>=0.5.3  # Medical text processing
medspacy>=1.0.0  # Medical NLP

# Development
pytest>=7.4.0
black>=23.9.0
flake8>=6.1.0
pre-commit>=3.5.0
jupyter>=1.0.0

# Optional GPU acceleration
# flash-attn>=2.3.0  # Uncomment if using GPU with flash attention
# vllm>=0.2.0        # Uncomment for faster inference
