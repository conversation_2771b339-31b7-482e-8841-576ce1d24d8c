# -*- coding: utf-8 -*-
"""
Dataset Loader Module / 数据集加载模块

This module handles loading and preprocessing of medical datasets for the GRAG system.
该模块处理GRAG系统的医疗数据集加载和预处理。
"""

import os
import json
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from datasets import load_dataset, Dataset
from sklearn.model_selection import train_test_split
from pathlib import Path
try:
    from ..utils import get_logger, get_config_manager, clean_text, extract_medical_entities
    config_manager = get_config_manager()
except ImportError:
    # Fallback for direct execution
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from utils import get_logger, get_config_manager, clean_text, extract_medical_entities
    config_manager = get_config_manager()

logger = get_logger(__name__)


class MedicalDatasetLoader:
    """Medical dataset loader for GRAG system / GRAG系统的医疗数据集加载器"""

    def __init__(self, dataset_name: str = None):
        """
        Initialize dataset loader / 初始化数据集加载器

        Args:
            dataset_name: Name of the dataset to load / 要加载的数据集名称
        """
        self.dataset_name = dataset_name or config_manager.data.dataset_name
        self.data_config = config_manager.data
        self.dataset = None
        self.processed_data = None
        logger.info(f"Initialized dataset loader for: {self.dataset_name}")

    def load_raw_dataset(self, split: Optional[str] = None, streaming: bool = False) -> Dataset:
        """
        Load raw dataset from HuggingFace / 从HuggingFace加载原始数据集

        Args:
            split: Dataset split to load / 要加载的数据集分割
            streaming: Whether to use streaming mode / 是否使用流式模式

        Returns:
            Loaded dataset / 加载的数据集
        """
        logger.info(f"Loading dataset: {self.dataset_name}")

        try:
            if split:
                dataset = load_dataset(self.dataset_name, split=split, streaming=streaming)
                self.dataset = dataset
            else:
                dataset = load_dataset(self.dataset_name, streaming=streaming)

                # Get train split (assuming it's the main split) / 获取训练分割
                if "train" in dataset:
                    data = dataset["train"]
                else:
                    # If no train split, use the first available split / 如果没有训练分割，使用第一个可用分割
                    split_name = list(dataset.keys())[0]
                    data = dataset[split_name]
                    logger.warning(f"No 'train' split found, using '{split_name}'")

                self.dataset = data

            # Limit samples if specified / 如果指定则限制样本数
            max_samples = self.data_config.max_samples
            if max_samples and len(self.dataset) > max_samples:
                self.dataset = self.dataset.select(range(max_samples))
                logger.info(f"Limited dataset to {max_samples} samples")

            logger.info(f"Loaded {len(self.dataset)} samples")
            return self.dataset

        except Exception as e:
            logger.error(f"Failed to load dataset: {e}")
            raise

    def preprocess_data(self, dataset: Dataset = None) -> List[Dict]:
        """
        Preprocess the dataset for training / 预处理训练数据集

        Args:
            dataset: Dataset to preprocess (uses self.dataset if None) / 要预处理的数据集

        Returns:
            List of preprocessed data items / 预处理后的数据项列表
        """
        if dataset is None:
            dataset = self.dataset

        if dataset is None:
            raise ValueError("No dataset available. Call load_raw_dataset() first.")

        logger.info("Preprocessing dataset...")

        processed_data = []
        preprocessing_config = self.data_config.preprocessing

        for item in dataset:
            # Extract query and response with flexible field names / 灵活提取查询和回复字段
            query = self._extract_field(item, ["query", "question", "input", "prompt", "patient_question"])
            response = self._extract_field(item, ["response", "answer", "output", "doctor_response", "reply"])

            # Skip empty entries / 跳过空条目
            if not query or not response:
                continue

            # Clean and normalize text / 清理和标准化文本
            query = clean_text(query) if preprocessing_config.get("clean_text", True) else query.strip()
            response = clean_text(response) if preprocessing_config.get("clean_text", True) else response.strip()

            # Length filtering / 长度过滤
            min_length = preprocessing_config.get("min_length", 10)
            max_length = preprocessing_config.get("max_length", 2048)

            if len(query) < min_length or len(response) < min_length:
                continue
            if len(query) > max_length or len(response) > max_length:
                continue

            # Extract medical entities for metadata / 提取医疗实体作为元数据
            medical_entities = extract_medical_entities(query + " " + response)

            processed_item = {
                "query": query,
                "response": response,
                "medical_entities": medical_entities,
                "messages": [
                    {"role": "system", "content": "你是一个专业的医疗助手，能够提供准确、安全的医疗建议。请基于医学知识和临床经验，为患者提供有用的健康指导。"},
                    {"role": "user", "content": query},
                    {"role": "assistant", "content": response}
                ],
                "metadata": {
                    "source": self.dataset_name,
                    "type": "medical_qa",
                    "query_length": len(query),
                    "response_length": len(response)
                }
            }

            processed_data.append(processed_item)

        self.processed_data = processed_data
        logger.info(f"Preprocessed {len(processed_data)} valid samples")
        return processed_data

    def _extract_field(self, item: Dict, field_names: List[str]) -> str:
        """
        Extract field value with multiple possible field names / 使用多个可能的字段名提取字段值

        Args:
            item: Data item / 数据项
            field_names: List of possible field names / 可能的字段名列表

        Returns:
            Field value or empty string / 字段值或空字符串
        """
        for field_name in field_names:
            if field_name in item and item[field_name]:
                return str(item[field_name]).strip()
        return ""

    def split_dataset(self, data: List[Dict] = None) -> Tuple[List[Dict], List[Dict], List[Dict]]:
        """
        Split dataset into train/val/test / 将数据集分割为训练/验证/测试集

        Args:
            data: Data to split (uses self.processed_data if None) / 要分割的数据

        Returns:
            Tuple of (train_data, val_data, test_data) / (训练数据, 验证数据, 测试数据)元组
        """
        if data is None:
            data = self.processed_data

        if data is None:
            raise ValueError("No data available. Call preprocess_data() first.")

        logger.info("Splitting dataset...")

        # Get split ratios from config / 从配置获取分割比例
        train_split = self.data_config.train_split
        val_split = self.data_config.val_split
        test_split = self.data_config.test_split

        # First split: train + val vs test / 第一次分割：训练+验证 vs 测试
        train_val, test = train_test_split(
            data,
            test_size=test_split,
            random_state=42,
            shuffle=True
        )

        # Second split: train vs val / 第二次分割：训练 vs 验证
        val_size = val_split / (train_split + val_split)
        train, val = train_test_split(
            train_val,
            test_size=val_size,
            random_state=42,
            shuffle=True
        )

        logger.info(f"Split sizes - Train: {len(train)}, Val: {len(val)}, Test: {len(test)}")
        return train, val, test

    def save_processed_data(self, train_data: List[Dict], val_data: List[Dict], test_data: List[Dict],
                          output_dir: str = "data/processed"):
        """
        Save processed data to files / 保存处理后的数据到文件

        Args:
            train_data: Training data / 训练数据
            val_data: Validation data / 验证数据
            test_data: Test data / 测试数据
            output_dir: Output directory / 输出目录
        """
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        # Save as JSON Lines format / 保存为JSON Lines格式
        for split_name, data in [("train", train_data), ("val", val_data), ("test", test_data)]:
            filepath = output_path / f"{split_name}.jsonl"
            with open(filepath, "w", encoding="utf-8") as f:
                for item in data:
                    f.write(json.dumps(item, ensure_ascii=False) + "\n")
            logger.info(f"Saved {len(data)} samples to {filepath}")

    def load_processed_data(self, split: str, data_dir: str = "data/processed") -> List[Dict]:
        """
        Load processed data from file / 从文件加载处理后的数据

        Args:
            split: Data split name (train/val/test) / 数据分割名称
            data_dir: Data directory / 数据目录

        Returns:
            List of data items / 数据项列表
        """
        filepath = Path(data_dir) / f"{split}.jsonl"

        if not filepath.exists():
            raise FileNotFoundError(f"Processed data file not found: {filepath}")

        data = []
        with open(filepath, "r", encoding="utf-8") as f:
            for line in f:
                data.append(json.loads(line.strip()))

        logger.info(f"Loaded {len(data)} samples from {filepath}")
        return data

    def prepare_sft_data(self, data: List[Dict]) -> List[Dict]:
        """
        Prepare data for SFT training / 准备SFT训练数据

        Args:
            data: Processed data / 处理后的数据

        Returns:
            SFT training data / SFT训练数据
        """
        sft_data = []

        for item in data:
            # Format for SFT training / SFT训练格式
            sft_item = {
                "messages": item["messages"],
                "metadata": item.get("metadata", {})
            }
            sft_data.append(sft_item)

        logger.info(f"Prepared {len(sft_data)} samples for SFT training")
        return sft_data

    def prepare_grpo_data(self, data: List[Dict]) -> List[Dict]:
        """
        Prepare data for GRPO training / 准备GRPO训练数据

        Args:
            data: Processed data / 处理后的数据

        Returns:
            GRPO training data / GRPO训练数据
        """
        grpo_data = []

        for item in data:
            # Format for GRPO training (only prompt, response will be generated) / GRPO训练格式
            grpo_item = {
                "prompt": [
                    {"role": "system", "content": "你是一个专业的医疗助手，能够提供准确、安全的医疗建议。请基于医学知识和临床经验，为患者提供有用的健康指导。"},
                    {"role": "user", "content": item["query"]}
                ],
                "answer": item["response"],  # Ground truth for reward calculation / 用于奖励计算的真实答案
                "medical_entities": item.get("medical_entities", {}),
                "metadata": item.get("metadata", {})
            }
            grpo_data.append(grpo_item)

        logger.info(f"Prepared {len(grpo_data)} samples for GRPO training")
        return grpo_data

    def create_knowledge_base(self, data: List[Dict]) -> List[Dict]:
        """
        Create knowledge base for retrieval / 创建检索知识库

        Args:
            data: Processed data / 处理后的数据

        Returns:
            Knowledge base items / 知识库项目
        """
        knowledge_base = []

        for idx, item in enumerate(data):
            kb_item = {
                "id": idx,
                "query": item["query"],
                "response": item["response"],
                "text": f"问题：{item['query']}\n回答：{item['response']}",
                "medical_entities": item.get("medical_entities", {}),
                "metadata": {
                    "source": self.dataset_name,
                    "type": "medical_qa",
                    "query_length": len(item["query"]),
                    "response_length": len(item["response"]),
                    **item.get("metadata", {})
                }
            }
            knowledge_base.append(kb_item)

        logger.info(f"Created knowledge base with {len(knowledge_base)} items")
        return knowledge_base

    def save_knowledge_base(self, knowledge_base: List[Dict], output_path: str = "data/processed/knowledge_base.jsonl"):
        """
        Save knowledge base to file / 保存知识库到文件

        Args:
            knowledge_base: Knowledge base items / 知识库项目
            output_path: Output file path / 输出文件路径
        """
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)

        with open(output_file, "w", encoding="utf-8") as f:
            for item in knowledge_base:
                f.write(json.dumps(item, ensure_ascii=False) + "\n")

        logger.info(f"Saved knowledge base with {len(knowledge_base)} items to {output_path}")


def main():
    """Test the dataset loader / 测试数据集加载器"""
    try:
        # Initialize loader / 初始化加载器
        loader = MedicalDatasetLoader()

        # Load and process data / 加载和处理数据
        raw_dataset = loader.load_raw_dataset()
        processed_data = loader.preprocess_data(raw_dataset)
        train_data, val_data, test_data = loader.split_dataset(processed_data)

        # Save processed data / 保存处理后的数据
        loader.save_processed_data(train_data, val_data, test_data)

        # Prepare training data / 准备训练数据
        sft_data = loader.prepare_sft_data(train_data)
        grpo_data = loader.prepare_grpo_data(train_data)

        # Create and save knowledge base / 创建和保存知识库
        knowledge_base = loader.create_knowledge_base(train_data + val_data)
        loader.save_knowledge_base(knowledge_base)

        logger.info("Dataset processing completed successfully!")

    except Exception as e:
        logger.error(f"Error in dataset processing: {e}")
        raise


if __name__ == "__main__":
    main()
