# GRAG Project .gitignore
# Medical RAG System with GRPO Optimization

# ============================================================================
# Python
# ============================================================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# ============================================================================
# Machine Learning & AI
# ============================================================================

# Model files
*.bin
*.safetensors
*.ckpt
*.pth
*.pt
*.h5
*.hdf5
*.pkl
*.pickle
*.joblib

# Model directories
models/*/
!models/.gitkeep
!models/README.md

# Checkpoints
checkpoints/
checkpoint-*/
runs/
lightning_logs/

# Weights & Biases
wandb/

# TensorBoard
tensorboard_logs/
tb_logs/

# MLflow
mlruns/
mlartifacts/

# Hugging Face cache
.cache/
transformers_cache/
huggingface_hub/

# ============================================================================
# Data Files
# ============================================================================

# Large data files
*.csv
*.json
*.jsonl
*.parquet
*.feather
*.arrow
*.tsv
*.txt
*.xml

# Compressed files
*.zip
*.tar.gz
*.rar
*.7z

# Data directories
data/raw/
data/processed/
data/interim/
data/external/
data/embeddings/
!data/.gitkeep
!data/README.md
!data/sample/

# Vector databases
*.index
*.faiss
chromadb/
qdrant_storage/
pinecone_cache/

# ============================================================================
# Logs and Outputs
# ============================================================================

# Log files
*.log
logs/
!logs/.gitkeep

# Output files
outputs/
results/
experiments/

# Temporary files
tmp/
temp/
.tmp/

# ============================================================================
# IDE and Editors
# ============================================================================

# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-workspace
*.sublime-project

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ============================================================================
# Operating System
# ============================================================================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ============================================================================
# Docker
# ============================================================================

# Docker files (keep configuration, ignore runtime)
.dockerignore
!docker/
!Dockerfile*
!docker-compose*.yml

# ============================================================================
# Security and Secrets
# ============================================================================

# Environment variables
.env
.env.local
.env.*.local
.env.production
.env.development

# API keys and secrets
secrets/
*.key
*.pem
*.crt
*.p12
*.pfx

# Configuration with sensitive data
config.local.yaml
config.prod.yaml
*secret*
*password*
*token*

# ============================================================================
# Project Specific
# ============================================================================

# GRAG specific ignores
cache/
.cache/

# Streamlit
.streamlit/

# FastAPI
.pytest_cache/

# Medical data (sensitive)
medical_data/
patient_data/
clinical_notes/

# Evaluation results
eval_results/
benchmark_results/

# Generated documentation
docs/build/
docs/_build/

# Backup files
*.bak
*.backup
*.old

# ============================================================================
# Miscellaneous
# ============================================================================

# Compressed archives
*.zip
*.tar.gz
*.tgz
*.tar.bz2
*.tbz2
*.tar.xz
*.txz

# Image files (unless specifically needed)
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.svg
!assets/
!static/images/

# Video files
*.mp4
*.avi
*.mov
*.wmv
*.flv

# Audio files
*.mp3
*.wav
*.flac
*.aac

# Large text files
*.txt
!requirements*.txt
!README*.txt
!LICENSE*.txt

# Node.js (if using any JS tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ============================================================================
# Keep Important Files
# ============================================================================

# Force include important files
!.gitkeep
!README.md
!LICENSE
!requirements.txt
!requirements-dev.txt
!Makefile
!setup.py
!pyproject.toml
!configs/config.yaml
!configs/config.example.yaml
!.env.example
