# -*- coding: utf-8 -*-
"""
Medical RAG Graph / 医疗RAG图

This module implements the main LangGraph workflow for medical RAG system.
该模块实现医疗RAG系统的主要LangGraph工作流。
"""

from typing import Dict, List, Any, Optional, TypedDict
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage

from ..utils import get_logger, config_manager
from .nodes import (
    QueryAnalysisNode,
    RetrievalNode,
    RerankingNode,
    GenerationNode,
    SelfCorrectionNode,
    WebSearchNode,
    FinalAnswerNode
)

logger = get_logger(__name__)


class GraphState(TypedDict):
    """Graph state definition / 图状态定义"""
    # Input / 输入
    query: str
    messages: List[BaseMessage]
    
    # Query analysis / 查询分析
    query_type: str
    medical_entities: Dict[str, Any]
    urgency_level: str
    needs_web_search: bool
    
    # Retrieval / 检索
    retrieved_docs: List[Dict[str, Any]]
    retrieval_scores: List[float]
    
    # Reranking / 重排序
    reranked_docs: List[Dict[str, Any]]
    rerank_scores: List[float]
    
    # Web search / 网络搜索
    web_results: List[Dict[str, Any]]
    
    # Generation / 生成
    generated_response: str
    confidence_score: float
    safety_check: Dict[str, Any]
    
    # Self-correction / 自我修正
    correction_needed: bool
    correction_attempts: int
    correction_feedback: str
    
    # Final output / 最终输出
    final_answer: str
    sources: List[Dict[str, Any]]
    metadata: Dict[str, Any]


class MedicalRAGGraph:
    """Medical RAG workflow graph / 医疗RAG工作流图"""
    
    def __init__(self):
        """Initialize medical RAG graph / 初始化医疗RAG图"""
        self.config = config_manager.langgraph
        
        # Initialize nodes / 初始化节点
        self.query_analysis_node = QueryAnalysisNode()
        self.retrieval_node = RetrievalNode()
        self.reranking_node = RerankingNode()
        self.generation_node = GenerationNode()
        self.self_correction_node = SelfCorrectionNode()
        self.web_search_node = WebSearchNode()
        self.final_answer_node = FinalAnswerNode()
        
        # Build graph / 构建图
        self.graph = self._build_graph()
        
        logger.info("Initialized MedicalRAGGraph")
    
    def _build_graph(self) -> StateGraph:
        """Build the workflow graph / 构建工作流图"""
        # Create graph / 创建图
        workflow = StateGraph(GraphState)
        
        # Add nodes / 添加节点
        workflow.add_node("query_analysis", self.query_analysis_node.process)
        workflow.add_node("retrieval", self.retrieval_node.process)
        workflow.add_node("reranking", self.reranking_node.process)
        workflow.add_node("web_search", self.web_search_node.process)
        workflow.add_node("generation", self.generation_node.process)
        workflow.add_node("self_correction", self.self_correction_node.process)
        workflow.add_node("final_answer", self.final_answer_node.process)
        
        # Set entry point / 设置入口点
        workflow.set_entry_point("query_analysis")
        
        # Add conditional edges / 添加条件边
        workflow.add_conditional_edges(
            "query_analysis",
            self._route_after_query_analysis,
            {
                "web_search": "web_search",
                "retrieval": "retrieval"
            }
        )
        
        workflow.add_edge("web_search", "retrieval")
        workflow.add_edge("retrieval", "reranking")
        workflow.add_edge("reranking", "generation")
        
        workflow.add_conditional_edges(
            "generation",
            self._route_after_generation,
            {
                "self_correction": "self_correction",
                "final_answer": "final_answer"
            }
        )
        
        workflow.add_conditional_edges(
            "self_correction",
            self._route_after_correction,
            {
                "retrieval": "retrieval",
                "generation": "generation",
                "final_answer": "final_answer"
            }
        )
        
        workflow.add_edge("final_answer", END)
        
        return workflow.compile()
    
    def _route_after_query_analysis(self, state: GraphState) -> str:
        """
        Route after query analysis / 查询分析后的路由
        
        Args:
            state: Current graph state / 当前图状态
            
        Returns:
            Next node name / 下一个节点名称
        """
        if state.get("needs_web_search", False):
            logger.info("Routing to web search")
            return "web_search"
        else:
            logger.info("Routing to retrieval")
            return "retrieval"
    
    def _route_after_generation(self, state: GraphState) -> str:
        """
        Route after generation / 生成后的路由
        
        Args:
            state: Current graph state / 当前图状态
            
        Returns:
            Next node name / 下一个节点名称
        """
        confidence_score = state.get("confidence_score", 0.0)
        safety_check = state.get("safety_check", {})
        
        # Check if correction is needed / 检查是否需要修正
        needs_correction = (
            confidence_score < 0.7 or
            not safety_check.get("is_safe", True) or
            state.get("correction_attempts", 0) == 0  # Always try correction once
        )
        
        if needs_correction and state.get("correction_attempts", 0) < 3:
            logger.info("Routing to self correction")
            return "self_correction"
        else:
            logger.info("Routing to final answer")
            return "final_answer"
    
    def _route_after_correction(self, state: GraphState) -> str:
        """
        Route after self-correction / 自我修正后的路由
        
        Args:
            state: Current graph state / 当前图状态
            
        Returns:
            Next node name / 下一个节点名称
        """
        correction_feedback = state.get("correction_feedback", "")
        correction_attempts = state.get("correction_attempts", 0)
        
        if correction_attempts >= 3:
            logger.info("Max correction attempts reached, routing to final answer")
            return "final_answer"
        
        if "retrieval" in correction_feedback.lower():
            logger.info("Routing back to retrieval for better context")
            return "retrieval"
        elif "generation" in correction_feedback.lower():
            logger.info("Routing back to generation")
            return "generation"
        else:
            logger.info("Routing to final answer")
            return "final_answer"
    
    def process_query(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Process a medical query through the RAG workflow / 通过RAG工作流处理医疗查询
        
        Args:
            query: User query / 用户查询
            **kwargs: Additional parameters / 额外参数
            
        Returns:
            Processing result / 处理结果
        """
        logger.info(f"Processing query: {query[:50]}...")
        
        # Initialize state / 初始化状态
        initial_state = GraphState(
            query=query,
            messages=[],
            query_type="",
            medical_entities={},
            urgency_level="normal",
            needs_web_search=False,
            retrieved_docs=[],
            retrieval_scores=[],
            reranked_docs=[],
            rerank_scores=[],
            web_results=[],
            generated_response="",
            confidence_score=0.0,
            safety_check={},
            correction_needed=False,
            correction_attempts=0,
            correction_feedback="",
            final_answer="",
            sources=[],
            metadata={}
        )
        
        # Add any additional state from kwargs / 从kwargs添加额外状态
        for key, value in kwargs.items():
            if key in initial_state:
                initial_state[key] = value
        
        try:
            # Run the graph / 运行图
            result = self.graph.invoke(initial_state)
            
            # Extract final result / 提取最终结果
            final_result = {
                "answer": result.get("final_answer", ""),
                "confidence": result.get("confidence_score", 0.0),
                "safety": result.get("safety_check", {}),
                "sources": result.get("sources", []),
                "metadata": {
                    "query_type": result.get("query_type", ""),
                    "medical_entities": result.get("medical_entities", {}),
                    "urgency_level": result.get("urgency_level", "normal"),
                    "correction_attempts": result.get("correction_attempts", 0),
                    "web_search_used": bool(result.get("web_results", [])),
                    **result.get("metadata", {})
                }
            }
            
            logger.info("Query processing completed successfully")
            return final_result
            
        except Exception as e:
            logger.error(f"Error processing query: {e}")
            return {
                "answer": "抱歉，处理您的查询时出现错误。请稍后重试。",
                "confidence": 0.0,
                "safety": {"is_safe": True, "warnings": []},
                "sources": [],
                "metadata": {"error": str(e)}
            }
    
    def batch_process(self, queries: List[str], **kwargs) -> List[Dict[str, Any]]:
        """
        Process multiple queries / 处理多个查询
        
        Args:
            queries: List of queries / 查询列表
            **kwargs: Additional parameters / 额外参数
            
        Returns:
            List of processing results / 处理结果列表
        """
        logger.info(f"Batch processing {len(queries)} queries")
        
        results = []
        for i, query in enumerate(queries):
            try:
                result = self.process_query(query, **kwargs)
                results.append(result)
                logger.debug(f"Processed query {i+1}/{len(queries)}")
            except Exception as e:
                logger.error(f"Error processing query {i+1}: {e}")
                results.append({
                    "answer": "处理查询时出现错误",
                    "confidence": 0.0,
                    "safety": {"is_safe": True, "warnings": []},
                    "sources": [],
                    "metadata": {"error": str(e)}
                })
        
        return results
    
    def get_graph_visualization(self) -> str:
        """
        Get graph visualization / 获取图可视化
        
        Returns:
            Graph visualization string / 图可视化字符串
        """
        try:
            # This would require additional dependencies for visualization
            # For now, return a text representation
            return """
Medical RAG Graph Workflow:

1. Query Analysis
   ├─ Analyze query type and medical entities
   ├─ Determine urgency level
   └─ Decide if web search is needed

2. Web Search (if needed)
   └─ Search for latest medical information

3. Retrieval
   ├─ Search vector database
   └─ Retrieve relevant documents

4. Reranking
   ├─ Rerank retrieved documents
   └─ Select top relevant documents

5. Generation
   ├─ Generate medical response
   ├─ Calculate confidence score
   └─ Perform safety check

6. Self-Correction (if needed)
   ├─ Analyze response quality
   ├─ Determine correction strategy
   └─ Route back to appropriate step

7. Final Answer
   ├─ Format final response
   ├─ Compile sources
   └─ Add metadata
            """
        except Exception as e:
            logger.warning(f"Could not generate graph visualization: {e}")
            return "Graph visualization not available"


def main():
    """Test medical RAG graph / 测试医疗RAG图"""
    try:
        # Initialize graph / 初始化图
        rag_graph = MedicalRAGGraph()
        
        # Test query / 测试查询
        test_query = "我最近总是头痛，应该怎么办？"
        
        # Process query / 处理查询
        result = rag_graph.process_query(test_query)
        
        print(f"Query: {test_query}")
        print(f"Answer: {result['answer']}")
        print(f"Confidence: {result['confidence']}")
        print(f"Safety: {result['safety']}")
        print(f"Sources: {len(result['sources'])} sources")
        print(f"Metadata: {result['metadata']}")
        
        # Print graph visualization / 打印图可视化
        print("\nGraph Structure:")
        print(rag_graph.get_graph_visualization())
        
        logger.info("Medical RAG graph test completed successfully!")
        
    except Exception as e:
        logger.error(f"Error in medical RAG graph test: {e}")
        raise


if __name__ == "__main__":
    main()
