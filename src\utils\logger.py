# -*- coding: utf-8 -*-
"""
Logging Module / 日志模块

This module provides centralized logging functionality for the GRAG system.
该模块为GRAG系统提供集中式日志功能。
"""

import sys
from pathlib import Path
from loguru import logger
from typing import Optional
from .config import get_config_manager


class LoggerManager:
    """Logger manager / 日志管理器"""
    
    def __init__(self):
        """Initialize logger manager / 初始化日志管理器"""
        self._setup_logger()
    
    def _setup_logger(self):
        """Setup logger configuration / 设置日志配置"""
        # Remove default handler / 移除默认处理器
        logger.remove()

        # Get logging configuration / 获取日志配置
        try:
            config_manager = get_config_manager()
            log_config = config_manager.logging_config
        except Exception:
            # Fallback to default configuration if config loading fails
            # 如果配置加载失败，回退到默认配置
            self._setup_default_logger()
            return
        
        # Ensure log directory exists / 确保日志目录存在
        log_file = Path(log_config.file)
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Add console handler / 添加控制台处理器
        logger.add(
            sys.stdout,
            level=log_config.level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
                   "<level>{message}</level>",
            colorize=True
        )
        
        # Add file handler / 添加文件处理器
        logger.add(
            log_config.file,
            level=log_config.level,
            format=log_config.format,
            rotation=log_config.rotation,
            retention=log_config.retention,
            encoding="utf-8"
        )

    def _setup_default_logger(self):
        """Setup default logger configuration / 设置默认日志配置"""
        # Add console handler with default settings / 添加默认设置的控制台处理器
        logger.add(
            sys.stdout,
            level="INFO",
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
                   "<level>{message}</level>",
            colorize=True
        )

        # Add file handler with default settings / 添加默认设置的文件处理器
        log_file = Path("./logs/grag.log")
        log_file.parent.mkdir(parents=True, exist_ok=True)

        logger.add(
            str(log_file),
            level="INFO",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}",
            rotation="1 day",
            retention="30 days",
            encoding="utf-8"
        )
    
    def get_logger(self, name: Optional[str] = None):
        """
        Get logger instance / 获取日志实例
        
        Args:
            name: Logger name / 日志器名称
            
        Returns:
            Logger instance / 日志实例
        """
        if name:
            return logger.bind(name=name)
        return logger


# Global logger manager / 全局日志管理器
logger_manager = LoggerManager()

# Convenience function to get logger / 获取日志器的便利函数
def get_logger(name: Optional[str] = None):
    """
    Get logger instance / 获取日志实例
    
    Args:
        name: Logger name / 日志器名称
        
    Returns:
        Logger instance / 日志实例
    """
    return logger_manager.get_logger(name)


# Export logger for direct use / 导出日志器供直接使用
log = get_logger("GRAG")
