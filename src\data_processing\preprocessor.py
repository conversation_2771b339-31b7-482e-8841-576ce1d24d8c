# -*- coding: utf-8 -*-
"""
Data Preprocessor Module / 数据预处理模块

This module provides advanced preprocessing capabilities for medical text data.
该模块为医疗文本数据提供高级预处理功能。
"""

import re
import jieba
import opencc
from typing import List, Dict, Any, Optional, Tuple
from ..utils import get_logger, clean_text, extract_medical_entities

logger = get_logger(__name__)


class MedicalTextPreprocessor:
    """Medical text preprocessor / 医疗文本预处理器"""
    
    def __init__(self):
        """Initialize preprocessor / 初始化预处理器"""
        # Initialize OpenCC converter for traditional to simplified Chinese / 初始化繁简转换器
        self.converter = opencc.OpenCC('t2s')
        
        # Load medical vocabulary for jieba / 为jieba加载医疗词汇
        self._load_medical_vocabulary()
        
        logger.info("Medical text preprocessor initialized")
    
    def _load_medical_vocabulary(self):
        """Load medical vocabulary for better segmentation / 加载医疗词汇以改善分词"""
        # Common medical terms / 常见医疗术语
        medical_terms = [
            # Symptoms / 症状
            "头痛", "发热", "咳嗽", "腹痛", "胸痛", "呼吸困难", "恶心", "呕吐", "腹泻", "便秘",
            "失眠", "疲劳", "眩晕", "心悸", "气短", "肿胀", "红肿", "瘙痒", "麻木",
            
            # Diseases / 疾病
            "感冒", "发烧", "肺炎", "胃炎", "高血压", "糖尿病", "心脏病", "肝炎", "肾炎",
            "癌症", "肿瘤", "结石", "骨折", "扭伤", "过敏", "哮喘", "支气管炎",
            
            # Body parts / 身体部位
            "头部", "颈部", "胸部", "腹部", "背部", "四肢", "关节", "肌肉", "皮肤",
            "心脏", "肺部", "肝脏", "肾脏", "胃部", "肠道",
            
            # Treatments / 治疗
            "手术", "药物", "治疗", "康复", "理疗", "针灸", "按摩", "休息", "饮食调理"
        ]
        
        # Add medical terms to jieba dictionary / 将医疗术语添加到jieba词典
        for term in medical_terms:
            jieba.add_word(term)
    
    def preprocess_text(self, text: str, 
                       normalize_traditional: bool = True,
                       remove_special_chars: bool = True,
                       segment_words: bool = False) -> str:
        """
        Preprocess medical text / 预处理医疗文本
        
        Args:
            text: Input text / 输入文本
            normalize_traditional: Convert traditional to simplified Chinese / 繁体转简体
            remove_special_chars: Remove special characters / 移除特殊字符
            segment_words: Segment words with jieba / 使用jieba分词
            
        Returns:
            Preprocessed text / 预处理后的文本
        """
        if not text:
            return ""
        
        # Convert traditional to simplified Chinese / 繁体转简体
        if normalize_traditional:
            text = self.converter.convert(text)
        
        # Remove extra whitespace / 移除多余空白
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters if requested / 如果需要则移除特殊字符
        if remove_special_chars:
            # Keep Chinese characters, punctuation, numbers, and basic Latin / 保留中文字符、标点、数字和基本拉丁字符
            text = re.sub(r'[^\u4e00-\u9fff\u3000-\u303f\uff00-\uffef\w\s.,!?;:()（）【】「」《》""''。，！？；：]', '', text)
        
        # Normalize medical terminology / 标准化医疗术语
        text = self._normalize_medical_terms(text)
        
        # Segment words if requested / 如果需要则分词
        if segment_words:
            words = jieba.cut(text)
            text = ' '.join(words)
        
        return text.strip()
    
    def _normalize_medical_terms(self, text: str) -> str:
        """
        Normalize medical terminology / 标准化医疗术语
        
        Args:
            text: Input text / 输入文本
            
        Returns:
            Text with normalized medical terms / 标准化医疗术语后的文本
        """
        # Common medical term variations / 常见医疗术语变体
        normalizations = {
            # Symptoms / 症状
            r'(头疼|头痛)': '头痛',
            r'(发烧|发热)': '发热',
            r'(咳嗽|咳)': '咳嗽',
            r'(肚子疼|腹疼|腹痛)': '腹痛',
            r'(胸疼|胸痛)': '胸痛',
            r'(呼吸困难|气喘|喘不过气)': '呼吸困难',
            
            # Common expressions / 常见表达
            r'(怎么办|咋办)': '如何处理',
            r'(什么原因|为什么)': '原因',
            r'(需要注意什么|注意事项)': '注意事项',
        }
        
        for pattern, replacement in normalizations.items():
            text = re.sub(pattern, replacement, text)
        
        return text
    
    def extract_medical_context(self, text: str) -> Dict[str, Any]:
        """
        Extract medical context from text / 从文本中提取医疗上下文
        
        Args:
            text: Input medical text / 输入医疗文本
            
        Returns:
            Medical context information / 医疗上下文信息
        """
        context = {
            "symptoms": [],
            "diseases": [],
            "body_parts": [],
            "treatments": [],
            "urgency_level": "normal",  # normal, urgent, emergency
            "question_type": "general"  # general, symptom_inquiry, treatment_advice, etc.
        }
        
        # Extract medical entities / 提取医疗实体
        entities = extract_medical_entities(text)
        context.update(entities)
        
        # Determine urgency level / 判断紧急程度
        context["urgency_level"] = self._assess_urgency(text)
        
        # Classify question type / 分类问题类型
        context["question_type"] = self._classify_question_type(text)
        
        return context
    
    def _assess_urgency(self, text: str) -> str:
        """
        Assess urgency level of medical query / 评估医疗查询的紧急程度
        
        Args:
            text: Input text / 输入文本
            
        Returns:
            Urgency level / 紧急程度
        """
        # Emergency keywords / 紧急关键词
        emergency_keywords = [
            "急救", "紧急", "危险", "严重", "昏迷", "休克", "大出血", "呼吸停止",
            "心脏停止", "中毒", "窒息", "骨折", "烧伤"
        ]
        
        # Urgent keywords / 急迫关键词
        urgent_keywords = [
            "疼痛剧烈", "高烧", "呼吸困难", "胸痛", "腹痛剧烈", "头痛剧烈",
            "呕血", "便血", "意识模糊", "抽搐"
        ]
        
        text_lower = text.lower()
        
        for keyword in emergency_keywords:
            if keyword in text_lower:
                return "emergency"
        
        for keyword in urgent_keywords:
            if keyword in text_lower:
                return "urgent"
        
        return "normal"
    
    def _classify_question_type(self, text: str) -> str:
        """
        Classify the type of medical question / 分类医疗问题类型
        
        Args:
            text: Input text / 输入文本
            
        Returns:
            Question type / 问题类型
        """
        # Question type patterns / 问题类型模式
        patterns = {
            "symptom_inquiry": [r"什么症状", r"有什么表现", r"症状是", r"会出现"],
            "diagnosis_request": [r"是什么病", r"可能是", r"诊断", r"什么疾病"],
            "treatment_advice": [r"怎么治疗", r"如何治疗", r"吃什么药", r"怎么办"],
            "prevention": [r"如何预防", r"预防措施", r"注意什么", r"避免"],
            "medication": [r"药物", r"吃药", r"用药", r"服用"],
            "lifestyle": [r"饮食", r"运动", r"生活习惯", r"作息"]
        }
        
        for question_type, type_patterns in patterns.items():
            for pattern in type_patterns:
                if re.search(pattern, text):
                    return question_type
        
        return "general"
    
    def create_training_prompt(self, query: str, response: str, 
                             include_context: bool = True) -> Dict[str, Any]:
        """
        Create training prompt with medical context / 创建包含医疗上下文的训练提示
        
        Args:
            query: User query / 用户查询
            response: Expected response / 期望回复
            include_context: Whether to include medical context / 是否包含医疗上下文
            
        Returns:
            Training prompt data / 训练提示数据
        """
        # Preprocess query and response / 预处理查询和回复
        processed_query = self.preprocess_text(query)
        processed_response = self.preprocess_text(response)
        
        # Extract medical context / 提取医疗上下文
        context = self.extract_medical_context(processed_query) if include_context else {}
        
        # Create system prompt based on context / 基于上下文创建系统提示
        system_prompt = self._create_system_prompt(context)
        
        prompt_data = {
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": processed_query},
                {"role": "assistant", "content": processed_response}
            ],
            "medical_context": context,
            "metadata": {
                "urgency_level": context.get("urgency_level", "normal"),
                "question_type": context.get("question_type", "general"),
                "query_length": len(processed_query),
                "response_length": len(processed_response)
            }
        }
        
        return prompt_data
    
    def _create_system_prompt(self, context: Dict[str, Any]) -> str:
        """
        Create system prompt based on medical context / 基于医疗上下文创建系统提示
        
        Args:
            context: Medical context / 医疗上下文
            
        Returns:
            System prompt / 系统提示
        """
        base_prompt = "你是一个专业的医疗助手，能够提供准确、安全的医疗建议。"
        
        urgency = context.get("urgency_level", "normal")
        question_type = context.get("question_type", "general")
        
        if urgency == "emergency":
            base_prompt += "这是一个紧急医疗情况，请立即建议寻求专业医疗帮助。"
        elif urgency == "urgent":
            base_prompt += "这是一个需要及时关注的医疗问题，请提供适当的建议。"
        
        if question_type == "treatment_advice":
            base_prompt += "请提供安全、有效的治疗建议，并强调专业医疗咨询的重要性。"
        elif question_type == "medication":
            base_prompt += "在提供药物建议时，请特别注意安全性和专业医疗指导的必要性。"
        
        base_prompt += "请基于医学知识和临床经验，为患者提供有用的健康指导，同时强调专业医疗咨询的重要性。"
        
        return base_prompt
