# GRAG Scripts Execution Order / 脚本执行顺序

## Quick Start / 快速开始

Execute the scripts in numerical order for a complete setup:

按数字顺序执行脚本以完成完整设置：

```bash
# Step 0: Test setup (optional)
python scripts/00_test_setup.py

# Step 1: Download and process data
python scripts/01_download_data.py

# Step 2: Create vector store with progress tracking
python scripts/02_create_vector_store.py

# Step 3: Train SFT model
python scripts/03_train_sft.py

# Step 4: Train GRPO model  
python scripts/04_train_grpo.py

# Step 5: Run the application
python scripts/05_run_app.py

# Step 99: Test the complete system (optional)
python scripts/99_test_model.py
```

## Script Details / 脚本详情

| Script | Purpose | Duration | Output |
|--------|---------|----------|---------|
| `00_test_setup.py` | 🔧 Test environment setup | ~10s | Dependency verification |
| `01_download_data.py` | 📥 Download & process data | ~30s | Training data, knowledge base |
| `02_create_vector_store.py` | 🗂️ Create FAISS index | ~5-10min | Vector embeddings |
| `03_train_sft.py` | 🎓 Supervised fine-tuning | Variable | SFT model |
| `04_train_grpo.py` | 🎯 Policy optimization | Variable | GRPO model |
| `05_run_app.py` | 🌐 Launch web interface | Continuous | Streamlit app |
| `99_test_model.py` | ✅ System testing | ~2-5min | Test results |

## Key Features / 主要特性

### 📊 Progress Tracking
- Real-time progress bars
- ETA calculations  
- Batch processing updates
- Memory usage optimization

### 🔄 Error Recovery
- Graceful error handling
- Detailed logging
- Continuation from failures
- Resource cleanup

### 🎯 User-Friendly
- Clear step-by-step execution
- Intuitive naming convention
- Comprehensive documentation
- Visual progress indicators

## Prerequisites / 前提条件

1. **Environment / 环境**: Python 3.8+ with virtual environment activated
2. **Dependencies / 依赖**: `pip install -r requirements.txt`
3. **Hardware / 硬件**: 8GB+ RAM recommended

## Troubleshooting / 故障排除

### If a script fails / 如果脚本失败:

1. **Check the logs** for detailed error messages
2. **Verify prerequisites** are met
3. **Restart from the failed step** (scripts are designed to be re-runnable)
4. **Check available resources** (memory, disk space)

### Common solutions / 常见解决方案:

```bash
# Restart virtual environment
deactivate
source .venv/bin/activate  # Linux/Mac
.venv\Scripts\activate     # Windows

# Clear cache if needed
rm -rf scripts/__pycache__

# Check system resources
# Ensure sufficient memory and disk space
```

## Success Indicators / 成功指标

✅ **Step 1 Complete**: Knowledge base created with 900 items  
✅ **Step 2 Complete**: Vector store with 900 documents indexed  
✅ **Step 3 Complete**: SFT model trained and saved  
✅ **Step 4 Complete**: GRPO model optimized  
✅ **Step 5 Complete**: Web app accessible at http://localhost:8501  

## Next Steps / 下一步

After successful execution:

1. **Access the web interface** at http://localhost:8501
2. **Test medical queries** to verify functionality
3. **Customize configurations** as needed
4. **Review logs** for any warnings or optimization opportunities

For detailed information, see `docs/execution_guide.md`
