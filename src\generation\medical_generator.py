# -*- coding: utf-8 -*-
"""
Medical Text Generator / 医疗文本生成器

This module implements medical text generation with safety checks and confidence scoring.
该模块实现带有安全检查和置信度评分的医疗文本生成。
"""

import torch
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
from transformers import AutoTokenizer, AutoModelForCausalLM, GenerationConfig
from unsloth import FastLanguageModel
import re

from ..utils import get_logger, get_config_manager, efficient_tokenize
from .safety_checker import MedicalSafetyChecker
from .confidence_calculator import ConfidenceCalculator

logger = get_logger(__name__)


class MedicalGenerator:
    """Medical text generator / 医疗文本生成器"""
    
    def __init__(self, model_path: str = None, config: Dict[str, Any] = None):
        """
        Initialize medical generator / 初始化医疗生成器
        
        Args:
            model_path: Path to trained model / 训练模型路径
            config: Generation configuration / 生成配置
        """
        config_manager = get_config_manager()
        self.config = config or config_manager.model
        self.model_path = model_path or self.config.base_model
        
        self.model = None
        self.tokenizer = None
        self.safety_checker = MedicalSafetyChecker()
        self.confidence_calculator = ConfidenceCalculator()
        
        # Generation parameters / 生成参数
        self.generation_config = GenerationConfig(
            max_new_tokens=self.config.max_length,
            temperature=self.config.temperature,
            top_p=self.config.top_p,
            top_k=self.config.top_k,
            do_sample=True,
            pad_token_id=None,  # Will be set after loading tokenizer
            eos_token_id=None,  # Will be set after loading tokenizer
        )
        
        logger.info(f"Initialized MedicalGenerator with model: {self.model_path}")
    
    def load_model(self):
        """Load trained model / 加载训练模型"""
        logger.info(f"Loading model from: {self.model_path}")
        
        try:
            # Check if it's an Unsloth model / 检查是否为Unsloth模型
            if Path(self.model_path).exists() and "unsloth" in str(self.model_path).lower():
                # Load Unsloth model / 加载Unsloth模型
                self.model, self.tokenizer = FastLanguageModel.from_pretrained(
                    model_name=self.model_path,
                    max_seq_length=self.config.max_length,
                    load_in_4bit=self.config.load_in_4bit,
                    fast_inference=True,
                )
                
                # Enable fast inference / 启用快速推理
                FastLanguageModel.for_inference(self.model)
                
            else:
                # Load standard transformers model / 加载标准transformers模型
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
                self.model = AutoModelForCausalLM.from_pretrained(
                    self.model_path,
                    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                    device_map="auto" if torch.cuda.is_available() else None,
                    load_in_4bit=self.config.load_in_4bit,
                )
            
            # Set pad token / 设置填充token
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Update generation config / 更新生成配置
            self.generation_config.pad_token_id = self.tokenizer.pad_token_id
            self.generation_config.eos_token_id = self.tokenizer.eos_token_id
            
            logger.info("Model loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise
    
    def generate_response(
        self, 
        query: str, 
        context: List[str] = None,
        max_new_tokens: int = None,
        temperature: float = None,
        return_confidence: bool = True,
        check_safety: bool = True
    ) -> Dict[str, Any]:
        """
        Generate medical response / 生成医疗回复
        
        Args:
            query: User query / 用户查询
            context: Retrieved context / 检索到的上下文
            max_new_tokens: Maximum new tokens / 最大新token数
            temperature: Generation temperature / 生成温度
            return_confidence: Whether to return confidence score / 是否返回置信度分数
            check_safety: Whether to check safety / 是否检查安全性
            
        Returns:
            Generation result with response, confidence, and safety info / 包含回复、置信度和安全信息的生成结果
        """
        if self.model is None:
            self.load_model()
        
        logger.info(f"Generating response for query: {query[:50]}...")
        
        # Prepare input / 准备输入
        messages = self._prepare_messages(query, context)
        
        # Generate response / 生成回复
        response = self._generate_text(
            messages, 
            max_new_tokens=max_new_tokens,
            temperature=temperature
        )
        
        result = {
            "response": response,
            "query": query,
            "context": context or [],
        }
        
        # Calculate confidence / 计算置信度
        if return_confidence:
            confidence = self.confidence_calculator.calculate_confidence(
                query, response, context
            )
            result["confidence"] = confidence
        
        # Check safety / 检查安全性
        if check_safety:
            safety_result = self.safety_checker.check_safety(query, response)
            result["safety"] = safety_result
        
        logger.info("Response generated successfully")
        return result
    
    def _prepare_messages(self, query: str, context: List[str] = None) -> List[Dict[str, str]]:
        """
        Prepare messages for generation / 准备生成的消息
        
        Args:
            query: User query / 用户查询
            context: Retrieved context / 检索到的上下文
            
        Returns:
            Formatted messages / 格式化的消息
        """
        # System prompt / 系统提示
        system_prompt = """你是一个专业的医疗助手，能够提供准确、安全的医疗建议。请基于医学知识和临床经验，为患者提供有用的健康指导。

重要提醒：
1. 提供的建议仅供参考，不能替代专业医疗诊断
2. 对于严重症状，建议及时就医
3. 不要给出绝对性的诊断结论
4. 强调专业医疗咨询的重要性"""
        
        messages = [
            {"role": "system", "content": system_prompt}
        ]
        
        # Add context if available / 如果有上下文则添加
        if context:
            context_text = "\n\n".join(context[:3])  # Use top 3 contexts
            context_message = f"参考信息：\n{context_text}\n\n基于以上参考信息和你的医学知识，请回答以下问题："
            messages.append({"role": "user", "content": context_message})
        
        # Add user query / 添加用户查询
        messages.append({"role": "user", "content": query})
        
        return messages
    
    def _generate_text(
        self, 
        messages: List[Dict[str, str]], 
        max_new_tokens: int = None,
        temperature: float = None
    ) -> str:
        """
        Generate text using the model / 使用模型生成文本
        
        Args:
            messages: Input messages / 输入消息
            max_new_tokens: Maximum new tokens / 最大新token数
            temperature: Generation temperature / 生成温度
            
        Returns:
            Generated text / 生成的文本
        """
        # Apply chat template / 应用聊天模板
        if hasattr(self.tokenizer, 'apply_chat_template'):
            input_text = self.tokenizer.apply_chat_template(
                messages, 
                tokenize=False, 
                add_generation_prompt=True
            )
        else:
            # Fallback formatting / 回退格式化
            input_text = ""
            for message in messages:
                if message["role"] == "system":
                    input_text += f"系统：{message['content']}\n"
                elif message["role"] == "user":
                    input_text += f"用户：{message['content']}\n"
                elif message["role"] == "assistant":
                    input_text += f"助手：{message['content']}\n"
            input_text += "助手："
        
        # Tokenize input efficiently / 高效地标记化输入
        inputs = efficient_tokenize(
            self.tokenizer,
            input_text,
            return_tensors="pt",
            padding=True,
            truncation=True,
            max_length=self.config.max_length - (max_new_tokens or 512)
        )
        
        if torch.cuda.is_available():
            inputs = {k: v.cuda() for k, v in inputs.items()}
        
        # Update generation config / 更新生成配置
        gen_config = self.generation_config
        if max_new_tokens:
            gen_config.max_new_tokens = max_new_tokens
        if temperature:
            gen_config.temperature = temperature
        
        # Generate / 生成
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                generation_config=gen_config,
                pad_token_id=self.tokenizer.pad_token_id,
            )
        
        # Decode response / 解码回复
        input_length = inputs["input_ids"].shape[1]
        generated_tokens = outputs[0][input_length:]
        response = self.tokenizer.decode(generated_tokens, skip_special_tokens=True)
        
        # Clean response / 清理回复
        response = self._clean_response(response)
        
        return response
    
    def _clean_response(self, response: str) -> str:
        """
        Clean generated response / 清理生成的回复
        
        Args:
            response: Raw response / 原始回复
            
        Returns:
            Cleaned response / 清理后的回复
        """
        # Remove extra whitespace / 移除多余空白
        response = re.sub(r'\s+', ' ', response).strip()
        
        # Remove incomplete sentences at the end / 移除末尾不完整的句子
        sentences = response.split('。')
        if len(sentences) > 1 and len(sentences[-1].strip()) < 10:
            response = '。'.join(sentences[:-1]) + '。'
        
        # Ensure proper ending / 确保适当的结尾
        if not response.endswith(('。', '！', '？', '.')):
            response += '。'
        
        return response
    
    def batch_generate(
        self, 
        queries: List[str], 
        contexts: List[List[str]] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Generate responses for multiple queries / 为多个查询生成回复
        
        Args:
            queries: List of queries / 查询列表
            contexts: List of contexts for each query / 每个查询的上下文列表
            **kwargs: Additional generation arguments / 额外的生成参数
            
        Returns:
            List of generation results / 生成结果列表
        """
        logger.info(f"Batch generating responses for {len(queries)} queries")
        
        results = []
        contexts = contexts or [None] * len(queries)
        
        for query, context in zip(queries, contexts):
            try:
                result = self.generate_response(query, context, **kwargs)
                results.append(result)
            except Exception as e:
                logger.error(f"Error generating response for query: {e}")
                results.append({
                    "response": "抱歉，生成回复时出现错误。请稍后重试。",
                    "query": query,
                    "context": context or [],
                    "error": str(e)
                })
        
        return results


def main():
    """Test medical generator / 测试医疗生成器"""
    try:
        # Initialize generator / 初始化生成器
        generator = MedicalGenerator()
        
        # Load model / 加载模型
        generator.load_model()
        
        # Test generation / 测试生成
        test_query = "我最近总是头痛，应该怎么办？"
        result = generator.generate_response(test_query)
        
        print(f"Query: {result['query']}")
        print(f"Response: {result['response']}")
        print(f"Confidence: {result.get('confidence', 'N/A')}")
        print(f"Safety: {result.get('safety', 'N/A')}")
        
        logger.info("Medical generator test completed successfully!")
        
    except Exception as e:
        logger.error(f"Error in medical generator test: {e}")
        raise


if __name__ == "__main__":
    main()
