#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SFT Training Script / SFT训练脚本

This script trains the medical model using Supervised Fine-Tuning (SFT).
该脚本使用监督微调(SFT)训练医疗模型。
"""

import os
import sys
import argparse
import wandb
import torch
from pathlib import Path

# Add src to path / 添加src到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from utils import get_logger, config_manager, set_seed, efficient_tokenize
from training import SFTTrainer
from data_processing import MedicalDatasetLoader

logger = get_logger(__name__)


def train_sft_model(
    data_dir: str = "data/processed",
    output_dir: str = None,
    model_name: str = None,
    use_wandb: bool = False,
    resume_from_checkpoint: str = None
):
    """
    Train SFT model / 训练SFT模型
    
    Args:
        data_dir: Directory containing processed data / 包含处理数据的目录
        output_dir: Output directory for model / 模型输出目录
        model_name: Base model name / 基础模型名称
        use_wandb: Whether to use Weights & Biases / 是否使用Weights & Biases
        resume_from_checkpoint: Checkpoint to resume from / 要恢复的检查点
    """
    logger.info("Starting SFT training...")
    
    # Set random seed / 设置随机种子
    set_seed(config_manager.environment.seed)
    
    # Initialize Weights & Biases if requested / 如果请求则初始化Weights & Biases
    if use_wandb:
        wandb.init(
            project="medical-rag-sft",
            name=f"sft-{config_manager.model.base_model.split('/')[-1]}",
            config={
                "model": config_manager.model.__dict__,
                "training": config_manager.training.sft,
                "data": config_manager.data.__dict__
            }
        )
        logger.info("Weights & Biases initialized")
    
    # Update output directory / 更新输出目录
    if output_dir:
        config_manager.training.sft["output_dir"] = output_dir
    
    # Update model name / 更新模型名称
    if model_name:
        config_manager.model.base_model = model_name
    
    # Initialize trainer / 初始化训练器
    logger.info("Initializing SFT trainer...")
    trainer = SFTTrainer()
    
    # Load model / 加载模型
    logger.info(f"Loading model: {config_manager.model.base_model}")
    trainer.load_model()
    
    # Prepare datasets / 准备数据集
    logger.info("Preparing training datasets...")
    
    # Check if processed data exists / 检查处理数据是否存在
    train_file = Path(data_dir) / "train.jsonl"
    if not train_file.exists():
        logger.error(f"Training data not found at {train_file}")
        logger.info("Please run 'python scripts/download_data.py' first")
        sys.exit(1)
    
    # Load training data / 加载训练数据
    loader = MedicalDatasetLoader()
    train_data = loader.load_processed_data("train", data_dir)
    val_data = loader.load_processed_data("val", data_dir)
    
    # Prepare SFT format / 准备SFT格式
    sft_train_data = loader.prepare_sft_data(train_data)
    sft_val_data = loader.prepare_sft_data(val_data)
    
    # Convert to datasets / 转换为数据集
    train_dataset = trainer.prepare_dataset_from_data(sft_train_data)
    eval_dataset = trainer.prepare_dataset_from_data(sft_val_data)
    
    logger.info(f"Training samples: {len(train_dataset)}")
    logger.info(f"Validation samples: {len(eval_dataset)}")
    
    # Resume from checkpoint if specified / 如果指定则从检查点恢复
    if resume_from_checkpoint:
        logger.info(f"Resuming from checkpoint: {resume_from_checkpoint}")
        trainer.trainer.args.resume_from_checkpoint = resume_from_checkpoint
    
    # Start training / 开始训练
    logger.info("Starting SFT training...")
    trainer.train(train_dataset, eval_dataset)
    
    # Save model / 保存模型
    logger.info("Saving trained model...")
    trainer.save_model()
    
    # Evaluate model / 评估模型
    logger.info("Evaluating model...")
    eval_results = trainer.evaluate(eval_dataset)
    
    # Log results / 记录结果
    logger.info("Training completed successfully!")
    logger.info(f"Final evaluation results: {eval_results}")
    
    if use_wandb:
        wandb.log(eval_results)
        wandb.finish()
    
    # Test generation / 测试生成
    logger.info("Testing generation...")
    test_queries = [
        "我最近总是头痛，应该怎么办？",
        "发烧了需要注意什么？",
        "咳嗽一直不好，可能是什么原因？"
    ]
    
    for query in test_queries:
        try:
            # Simple generation test / 简单生成测试
            messages = [
                {"role": "system", "content": "你是一个专业的医疗助手。"},
                {"role": "user", "content": query}
            ]
            
            input_text = trainer.tokenizer.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            # Use efficient tokenization with all parameters at once
            inputs = efficient_tokenize(
                trainer.tokenizer,
                input_text,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=512
            )
            if hasattr(trainer.model, 'cuda'):
                inputs = {k: v.cuda() for k, v in inputs.items()}
            
            with torch.no_grad():
                outputs = trainer.model.generate(
                    **inputs,
                    max_new_tokens=256,
                    temperature=0.7,
                    do_sample=True,
                    pad_token_id=trainer.tokenizer.pad_token_id
                )
            
            response = trainer.tokenizer.decode(
                outputs[0][inputs['input_ids'].shape[1]:], 
                skip_special_tokens=True
            )
            
            logger.info(f"Query: {query}")
            logger.info(f"Response: {response}")
            logger.info("-" * 50)
            
        except Exception as e:
            logger.warning(f"Generation test failed for query '{query}': {e}")
    
    # Print summary / 打印摘要
    print("\n" + "="*60)
    print("SFT TRAINING COMPLETED / SFT训练完成")
    print("="*60)
    print(f"Model: {config_manager.model.base_model}")
    print(f"Output directory: {config_manager.training.sft['output_dir']}")
    print(f"Training samples: {len(train_dataset)}")
    print(f"Validation samples: {len(eval_dataset)}")
    print(f"Final evaluation: {eval_results}")
    print("="*60)
    print("NEXT STEPS / 下一步:")
    print("1. Run GRPO training: python scripts/train_grpo.py")
    print("2. Test the model: python scripts/test_model.py")
    print("3. Start frontend: streamlit run src/frontend/app.py")
    print("="*60)


def main():
    """Main function / 主函数"""
    parser = argparse.ArgumentParser(description="Train SFT model for medical RAG")
    
    parser.add_argument(
        "--data-dir",
        type=str,
        default="data/processed",
        help="Directory containing processed training data"
    )
    
    parser.add_argument(
        "--output-dir",
        type=str,
        default=None,
        help="Output directory for trained model (default: from config)"
    )
    
    parser.add_argument(
        "--model",
        type=str,
        default=None,
        help="Base model name (default: from config)"
    )
    
    parser.add_argument(
        "--wandb",
        action="store_true",
        help="Use Weights & Biases for logging"
    )
    
    parser.add_argument(
        "--resume",
        type=str,
        default=None,
        help="Resume training from checkpoint"
    )
    
    args = parser.parse_args()
    
    try:
        train_sft_model(
            data_dir=args.data_dir,
            output_dir=args.output_dir,
            model_name=args.model,
            use_wandb=args.wandb,
            resume_from_checkpoint=args.resume
        )
    except Exception as e:
        logger.error(f"Error in SFT training: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
