# -*- coding: utf-8 -*-
"""
Evaluation Metrics / 评估指标

This module implements various evaluation metrics for the medical RAG system.
该模块实现医疗RAG系统的各种评估指标。
"""

import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from abc import ABC, abstractmethod
import re

# NLP evaluation libraries / NLP评估库
from rouge_score import rouge_scorer
from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
from bert_score import score as bert_score
from sentence_transformers import SentenceTransformer

from ..utils import get_logger
from ..generation.safety_checker import MedicalSafetyChecker

logger = get_logger(__name__)


class BaseMetric(ABC):
    """Base evaluation metric / 基础评估指标"""
    
    def __init__(self, name: str):
        """
        Initialize metric / 初始化指标
        
        Args:
            name: Metric name / 指标名称
        """
        self.name = name
    
    @abstractmethod
    def calculate(self, predictions: List[str], references: List[str], **kwargs) -> Dict[str, float]:
        """
        Calculate metric scores / 计算指标分数
        
        Args:
            predictions: Predicted texts / 预测文本
            references: Reference texts / 参考文本
            **kwargs: Additional arguments / 额外参数
            
        Returns:
            Metric scores / 指标分数
        """
        pass


class RougeMetric(BaseMetric):
    """ROUGE metric for text similarity / ROUGE文本相似度指标"""
    
    def __init__(self):
        super().__init__("rouge")
        self.scorer = rouge_scorer.RougeScorer(['rouge1', 'rouge2', 'rougeL'], use_stemmer=False)
    
    def calculate(self, predictions: List[str], references: List[str], **kwargs) -> Dict[str, float]:
        """Calculate ROUGE scores / 计算ROUGE分数"""
        if len(predictions) != len(references):
            raise ValueError("Predictions and references must have the same length")
        
        rouge1_scores = []
        rouge2_scores = []
        rougeL_scores = []
        
        for pred, ref in zip(predictions, references):
            scores = self.scorer.score(ref, pred)
            rouge1_scores.append(scores['rouge1'].fmeasure)
            rouge2_scores.append(scores['rouge2'].fmeasure)
            rougeL_scores.append(scores['rougeL'].fmeasure)
        
        return {
            "rouge1": np.mean(rouge1_scores),
            "rouge2": np.mean(rouge2_scores),
            "rougeL": np.mean(rougeL_scores),
            "rouge_avg": np.mean([np.mean(rouge1_scores), np.mean(rouge2_scores), np.mean(rougeL_scores)])
        }


class BleuMetric(BaseMetric):
    """BLEU metric for text similarity / BLEU文本相似度指标"""
    
    def __init__(self):
        super().__init__("bleu")
        self.smoothing = SmoothingFunction().method1
    
    def calculate(self, predictions: List[str], references: List[str], **kwargs) -> Dict[str, float]:
        """Calculate BLEU scores / 计算BLEU分数"""
        if len(predictions) != len(references):
            raise ValueError("Predictions and references must have the same length")
        
        bleu_scores = []
        
        for pred, ref in zip(predictions, references):
            # Tokenize (simple character-level for Chinese) / 分词（中文使用简单字符级别）
            pred_tokens = list(pred.replace(' ', ''))
            ref_tokens = [list(ref.replace(' ', ''))]
            
            try:
                bleu = sentence_bleu(ref_tokens, pred_tokens, smoothing_function=self.smoothing)
                bleu_scores.append(bleu)
            except:
                bleu_scores.append(0.0)
        
        return {
            "bleu": np.mean(bleu_scores),
            "bleu_std": np.std(bleu_scores)
        }


class BertScoreMetric(BaseMetric):
    """BERTScore metric for semantic similarity / BERTScore语义相似度指标"""
    
    def __init__(self, model_name: str = "bert-base-chinese"):
        super().__init__("bert_score")
        self.model_name = model_name
    
    def calculate(self, predictions: List[str], references: List[str], **kwargs) -> Dict[str, float]:
        """Calculate BERTScore / 计算BERTScore"""
        if len(predictions) != len(references):
            raise ValueError("Predictions and references must have the same length")
        
        try:
            P, R, F1 = bert_score(predictions, references, model_type=self.model_name, lang="zh")
            
            return {
                "bert_precision": P.mean().item(),
                "bert_recall": R.mean().item(),
                "bert_f1": F1.mean().item()
            }
        except Exception as e:
            logger.warning(f"BERTScore calculation failed: {e}")
            return {
                "bert_precision": 0.0,
                "bert_recall": 0.0,
                "bert_f1": 0.0
            }


class MedicalAccuracyMetric(BaseMetric):
    """Medical accuracy metric / 医疗准确性指标"""
    
    def __init__(self):
        super().__init__("medical_accuracy")
        
        # Medical terminology / 医学术语
        self.medical_terms = [
            "症状", "诊断", "治疗", "药物", "疾病", "病因", "检查",
            "化验", "影像", "手术", "康复", "预防", "并发症", "副作用"
        ]
        
        # Professional expressions / 专业表达
        self.professional_expressions = [
            "建议咨询", "专业医生", "医院检查", "及时就医",
            "仅供参考", "因人而异", "具体情况"
        ]
        
        # Inappropriate expressions / 不当表达
        self.inappropriate_expressions = [
            "一定是", "肯定是", "绝对", "保证", "治愈",
            "不用看医生", "随便吃", "没关系"
        ]
    
    def calculate(self, predictions: List[str], references: List[str], **kwargs) -> Dict[str, float]:
        """Calculate medical accuracy scores / 计算医疗准确性分数"""
        accuracy_scores = []
        terminology_scores = []
        professionalism_scores = []
        
        for pred in predictions:
            # Medical terminology usage / 医学术语使用
            term_count = sum(1 for term in self.medical_terms if term in pred)
            terminology_score = min(1.0, term_count / 5)  # Normalize to max 5 terms
            terminology_scores.append(terminology_score)
            
            # Professional expression usage / 专业表达使用
            prof_count = sum(1 for expr in self.professional_expressions if expr in pred)
            professionalism_score = min(1.0, prof_count / 3)  # Normalize to max 3 expressions
            
            # Penalty for inappropriate expressions / 不当表达惩罚
            inappropriate_count = sum(1 for expr in self.inappropriate_expressions if expr in pred)
            professionalism_score -= inappropriate_count * 0.2
            professionalism_score = max(0.0, professionalism_score)
            
            professionalism_scores.append(professionalism_score)
            
            # Overall accuracy / 总体准确性
            accuracy = (terminology_score + professionalism_score) / 2
            accuracy_scores.append(accuracy)
        
        return {
            "medical_accuracy": np.mean(accuracy_scores),
            "terminology_usage": np.mean(terminology_scores),
            "professionalism": np.mean(professionalism_scores)
        }


class SafetyMetric(BaseMetric):
    """Safety metric for medical responses / 医疗回复安全性指标"""
    
    def __init__(self):
        super().__init__("safety")
        self.safety_checker = MedicalSafetyChecker()
    
    def calculate(self, predictions: List[str], references: List[str], queries: List[str] = None, **kwargs) -> Dict[str, float]:
        """Calculate safety scores / 计算安全性分数"""
        safety_scores = []
        violation_counts = []
        warning_counts = []
        
        queries = queries or [""] * len(predictions)
        
        for pred, query in zip(predictions, queries):
            safety_result = self.safety_checker.check_safety(query, pred)
            
            safety_scores.append(safety_result.safety_score)
            violation_counts.append(len(safety_result.violations))
            warning_counts.append(len(safety_result.warnings))
        
        return {
            "safety_score": np.mean(safety_scores),
            "avg_violations": np.mean(violation_counts),
            "avg_warnings": np.mean(warning_counts),
            "safe_responses_ratio": sum(1 for score in safety_scores if score >= 0.8) / len(safety_scores)
        }


class RelevanceMetric(BaseMetric):
    """Relevance metric using semantic similarity / 使用语义相似度的相关性指标"""
    
    def __init__(self, model_name: str = "BAAI/bge-large-zh-v1.5"):
        super().__init__("relevance")
        self.embedding_model = SentenceTransformer(model_name)
    
    def calculate(self, predictions: List[str], references: List[str], queries: List[str] = None, **kwargs) -> Dict[str, float]:
        """Calculate relevance scores / 计算相关性分数"""
        if queries is None:
            queries = references  # Use references as queries if not provided
        
        query_pred_similarities = []
        query_ref_similarities = []
        pred_ref_similarities = []
        
        # Generate embeddings / 生成嵌入
        query_embeddings = self.embedding_model.encode(queries)
        pred_embeddings = self.embedding_model.encode(predictions)
        ref_embeddings = self.embedding_model.encode(references)
        
        for i in range(len(predictions)):
            # Query-Prediction similarity / 查询-预测相似度
            qp_sim = np.dot(query_embeddings[i], pred_embeddings[i]) / (
                np.linalg.norm(query_embeddings[i]) * np.linalg.norm(pred_embeddings[i])
            )
            query_pred_similarities.append(qp_sim)
            
            # Query-Reference similarity / 查询-参考相似度
            qr_sim = np.dot(query_embeddings[i], ref_embeddings[i]) / (
                np.linalg.norm(query_embeddings[i]) * np.linalg.norm(ref_embeddings[i])
            )
            query_ref_similarities.append(qr_sim)
            
            # Prediction-Reference similarity / 预测-参考相似度
            pr_sim = np.dot(pred_embeddings[i], ref_embeddings[i]) / (
                np.linalg.norm(pred_embeddings[i]) * np.linalg.norm(ref_embeddings[i])
            )
            pred_ref_similarities.append(pr_sim)
        
        return {
            "query_pred_similarity": np.mean(query_pred_similarities),
            "query_ref_similarity": np.mean(query_ref_similarities),
            "pred_ref_similarity": np.mean(pred_ref_similarities),
            "relevance_score": np.mean(query_pred_similarities)  # Main relevance metric
        }


class RetrievalMetric(BaseMetric):
    """Retrieval evaluation metric / 检索评估指标"""
    
    def __init__(self):
        super().__init__("retrieval")
    
    def calculate(self, retrieved_docs: List[List[str]], relevant_docs: List[List[str]], k_values: List[int] = [1, 3, 5, 10], **kwargs) -> Dict[str, float]:
        """
        Calculate retrieval metrics / 计算检索指标
        
        Args:
            retrieved_docs: Retrieved documents for each query / 每个查询检索到的文档
            relevant_docs: Relevant documents for each query / 每个查询的相关文档
            k_values: K values for evaluation / 评估的K值
            
        Returns:
            Retrieval metrics / 检索指标
        """
        metrics = {}
        
        for k in k_values:
            precision_at_k = []
            recall_at_k = []
            
            for retrieved, relevant in zip(retrieved_docs, relevant_docs):
                # Take top-k retrieved documents / 取前k个检索文档
                top_k_retrieved = retrieved[:k]
                
                # Calculate precision@k / 计算precision@k
                relevant_retrieved = len(set(top_k_retrieved) & set(relevant))
                precision = relevant_retrieved / k if k > 0 else 0.0
                precision_at_k.append(precision)
                
                # Calculate recall@k / 计算recall@k
                recall = relevant_retrieved / len(relevant) if len(relevant) > 0 else 0.0
                recall_at_k.append(recall)
            
            metrics[f"precision@{k}"] = np.mean(precision_at_k)
            metrics[f"recall@{k}"] = np.mean(recall_at_k)
            
            # Calculate F1@k / 计算F1@k
            p_k = metrics[f"precision@{k}"]
            r_k = metrics[f"recall@{k}"]
            f1_k = 2 * p_k * r_k / (p_k + r_k) if (p_k + r_k) > 0 else 0.0
            metrics[f"f1@{k}"] = f1_k
        
        return metrics


def main():
    """Test evaluation metrics / 测试评估指标"""
    try:
        # Test data / 测试数据
        predictions = [
            "根据您的症状描述，可能是感冒引起的头痛。建议您多休息，多喝水，如果症状持续，请及时就医。",
            "这种情况建议您到医院进行详细检查，以确定具体病因。"
        ]
        
        references = [
            "感冒可能导致头痛，建议休息并及时就医。",
            "建议医院检查确定病因。"
        ]
        
        queries = [
            "我头痛怎么办？",
            "最近总是咳嗽，是什么原因？"
        ]
        
        # Test ROUGE / 测试ROUGE
        rouge_metric = RougeMetric()
        rouge_scores = rouge_metric.calculate(predictions, references)
        print("ROUGE Scores:", rouge_scores)
        
        # Test BLEU / 测试BLEU
        bleu_metric = BleuMetric()
        bleu_scores = bleu_metric.calculate(predictions, references)
        print("BLEU Scores:", bleu_scores)
        
        # Test Medical Accuracy / 测试医疗准确性
        medical_metric = MedicalAccuracyMetric()
        medical_scores = medical_metric.calculate(predictions, references)
        print("Medical Accuracy Scores:", medical_scores)
        
        # Test Safety / 测试安全性
        safety_metric = SafetyMetric()
        safety_scores = safety_metric.calculate(predictions, references, queries)
        print("Safety Scores:", safety_scores)
        
        # Test Relevance / 测试相关性
        relevance_metric = RelevanceMetric()
        relevance_scores = relevance_metric.calculate(predictions, references, queries)
        print("Relevance Scores:", relevance_scores)
        
        logger.info("Evaluation metrics test completed successfully!")
        
    except Exception as e:
        logger.error(f"Error in evaluation metrics test: {e}")
        raise


if __name__ == "__main__":
    main()
