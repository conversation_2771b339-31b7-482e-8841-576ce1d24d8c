# -*- coding: utf-8 -*-
"""
LangGraph Nodes / LangGraph节点

This module implements individual nodes for the medical RAG workflow.
该模块实现医疗RAG工作流的各个节点。
"""

import re
from typing import Dict, List, Any, Optional
from abc import ABC, abstractmethod

from ..utils import get_logger, config_manager
from ..retrieval import MedicalRetriever, MedicalReranker, MedicalWebSearcher
from ..generation import MedicalGenerator, MedicalSafetyChecker, ConfidenceCalculator

logger = get_logger(__name__)


class BaseNode(ABC):
    """Base node for workflow / 工作流基础节点"""
    
    def __init__(self, name: str):
        """
        Initialize base node / 初始化基础节点
        
        Args:
            name: Node name / 节点名称
        """
        self.name = name
        logger.debug(f"Initialized {name} node")
    
    @abstractmethod
    def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the state / 处理状态
        
        Args:
            state: Current state / 当前状态
            
        Returns:
            Updated state / 更新后的状态
        """
        pass


class QueryAnalysisNode(BaseNode):
    """Query analysis node / 查询分析节点"""
    
    def __init__(self):
        super().__init__("QueryAnalysis")
        
        # Medical entity patterns / 医疗实体模式
        self.symptom_patterns = [
            r"头痛|头疼|疼痛|痛|酸痛",
            r"发烧|发热|高烧|低烧",
            r"咳嗽|咳痰|干咳",
            r"恶心|呕吐|想吐",
            r"腹泻|拉肚子|便秘",
            r"失眠|睡不着|多梦",
            r"疲劳|乏力|没力气"
        ]
        
        self.urgency_keywords = [
            "急性", "严重", "剧烈", "突然", "大量出血",
            "呼吸困难", "胸痛", "昏迷", "休克"
        ]
        
        self.web_search_keywords = [
            "最新", "新药", "新治疗", "2024", "2025",
            "最近研究", "新发现", "新方法"
        ]
    
    def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Process query analysis / 处理查询分析"""
        query = state.get("query", "")
        logger.info(f"Analyzing query: {query[:50]}...")
        
        # Analyze query type / 分析查询类型
        query_type = self._classify_query_type(query)
        
        # Extract medical entities / 提取医疗实体
        medical_entities = self._extract_medical_entities(query)
        
        # Determine urgency level / 确定紧急程度
        urgency_level = self._determine_urgency(query)
        
        # Check if web search is needed / 检查是否需要网络搜索
        needs_web_search = self._needs_web_search(query)
        
        # Update state / 更新状态
        state.update({
            "query_type": query_type,
            "medical_entities": medical_entities,
            "urgency_level": urgency_level,
            "needs_web_search": needs_web_search
        })
        
        logger.info(f"Query analysis completed: type={query_type}, urgency={urgency_level}")
        return state
    
    def _classify_query_type(self, query: str) -> str:
        """Classify query type / 分类查询类型"""
        query_lower = query.lower()
        
        if any(word in query_lower for word in ["症状", "怎么办", "什么原因"]):
            return "symptom_inquiry"
        elif any(word in query_lower for word in ["药物", "吃什么药", "治疗"]):
            return "treatment_inquiry"
        elif any(word in query_lower for word in ["检查", "化验", "诊断"]):
            return "diagnostic_inquiry"
        elif any(word in query_lower for word in ["预防", "注意事项", "保健"]):
            return "prevention_inquiry"
        else:
            return "general_medical"
    
    def _extract_medical_entities(self, query: str) -> Dict[str, List[str]]:
        """Extract medical entities / 提取医疗实体"""
        entities = {
            "symptoms": [],
            "body_parts": [],
            "conditions": [],
            "medications": []
        }
        
        # Extract symptoms / 提取症状
        for pattern in self.symptom_patterns:
            matches = re.findall(pattern, query)
            entities["symptoms"].extend(matches)
        
        # Extract body parts / 提取身体部位
        body_parts = ["头", "胸", "腹", "背", "腿", "手", "脚", "眼", "耳", "鼻", "喉"]
        for part in body_parts:
            if part in query:
                entities["body_parts"].append(part)
        
        return entities
    
    def _determine_urgency(self, query: str) -> str:
        """Determine urgency level / 确定紧急程度"""
        if any(keyword in query for keyword in self.urgency_keywords):
            return "high"
        elif any(word in query for word in ["慢性", "长期", "一直"]):
            return "low"
        else:
            return "normal"
    
    def _needs_web_search(self, query: str) -> bool:
        """Check if web search is needed / 检查是否需要网络搜索"""
        return any(keyword in query for keyword in self.web_search_keywords)


class RetrievalNode(BaseNode):
    """Retrieval node / 检索节点"""
    
    def __init__(self):
        super().__init__("Retrieval")
        self.retriever = MedicalRetriever()
    
    def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Process retrieval / 处理检索"""
        query = state.get("query", "")
        urgency_level = state.get("urgency_level", "normal")
        
        logger.info(f"Retrieving documents for query: {query[:50]}...")
        
        # Adjust retrieval parameters based on urgency / 根据紧急程度调整检索参数
        top_k = 15 if urgency_level == "high" else 10
        similarity_threshold = 0.6 if urgency_level == "high" else 0.7
        
        # Retrieve documents / 检索文档
        retrieved_docs = self.retriever.retrieve(
            query=query,
            top_k=top_k,
            similarity_threshold=similarity_threshold,
            enable_self_correction=True
        )
        
        # Extract scores / 提取分数
        retrieval_scores = [doc.get("score", 0.0) for doc in retrieved_docs]
        
        # Update state / 更新状态
        state.update({
            "retrieved_docs": retrieved_docs,
            "retrieval_scores": retrieval_scores
        })
        
        logger.info(f"Retrieved {len(retrieved_docs)} documents")
        return state


class RerankingNode(BaseNode):
    """Reranking node / 重排序节点"""
    
    def __init__(self):
        super().__init__("Reranking")
        self.reranker = MedicalReranker()
    
    def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Process reranking / 处理重排序"""
        query = state.get("query", "")
        retrieved_docs = state.get("retrieved_docs", [])
        
        if not retrieved_docs:
            logger.warning("No documents to rerank")
            state.update({
                "reranked_docs": [],
                "rerank_scores": []
            })
            return state
        
        logger.info(f"Reranking {len(retrieved_docs)} documents")
        
        # Rerank documents / 重排序文档
        reranked_docs = self.reranker.rerank(query, retrieved_docs)
        
        # Extract rerank scores / 提取重排序分数
        rerank_scores = [doc.get("rerank_score", 0.0) for doc in reranked_docs]
        
        # Update state / 更新状态
        state.update({
            "reranked_docs": reranked_docs,
            "rerank_scores": rerank_scores
        })
        
        logger.info(f"Reranking completed, top score: {max(rerank_scores) if rerank_scores else 0}")
        return state


class WebSearchNode(BaseNode):
    """Web search node / 网络搜索节点"""
    
    def __init__(self):
        super().__init__("WebSearch")
        self.web_searcher = MedicalWebSearcher()
    
    def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Process web search / 处理网络搜索"""
        query = state.get("query", "")
        
        logger.info(f"Performing web search for: {query[:50]}...")
        
        try:
            # Perform web search / 执行网络搜索
            web_results = self.web_searcher.search(query, max_results=5)
            
            # Update state / 更新状态
            state.update({
                "web_results": web_results
            })
            
            logger.info(f"Web search completed, found {len(web_results)} results")
            
        except Exception as e:
            logger.error(f"Web search failed: {e}")
            state.update({
                "web_results": []
            })
        
        return state


class GenerationNode(BaseNode):
    """Generation node / 生成节点"""
    
    def __init__(self):
        super().__init__("Generation")
        self.generator = MedicalGenerator()
        self.confidence_calculator = ConfidenceCalculator()
        self.safety_checker = MedicalSafetyChecker()
    
    def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Process generation / 处理生成"""
        query = state.get("query", "")
        reranked_docs = state.get("reranked_docs", [])
        web_results = state.get("web_results", [])
        
        logger.info(f"Generating response for: {query[:50]}...")
        
        # Prepare context / 准备上下文
        context = []
        
        # Add reranked documents / 添加重排序文档
        for doc in reranked_docs[:3]:  # Top 3 documents
            context.append(doc.get("text", ""))
        
        # Add web search results / 添加网络搜索结果
        for result in web_results[:2]:  # Top 2 web results
            context.append(result.get("content", ""))
        
        # Generate response / 生成回复
        generation_result = self.generator.generate_response(
            query=query,
            context=context,
            return_confidence=True,
            check_safety=True
        )
        
        # Update state / 更新状态
        state.update({
            "generated_response": generation_result["response"],
            "confidence_score": generation_result.get("confidence", {}).get("overall_confidence", 0.0),
            "safety_check": generation_result.get("safety", {})
        })
        
        logger.info(f"Generation completed, confidence: {state['confidence_score']:.2f}")
        return state


class SelfCorrectionNode(BaseNode):
    """Self-correction node / 自我修正节点"""
    
    def __init__(self):
        super().__init__("SelfCorrection")
    
    def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Process self-correction / 处理自我修正"""
        generated_response = state.get("generated_response", "")
        confidence_score = state.get("confidence_score", 0.0)
        safety_check = state.get("safety_check", {})
        correction_attempts = state.get("correction_attempts", 0)
        
        logger.info(f"Performing self-correction, attempt {correction_attempts + 1}")
        
        # Analyze what needs correction / 分析需要修正的内容
        correction_feedback = self._analyze_correction_needs(
            generated_response, confidence_score, safety_check
        )
        
        # Update state / 更新状态
        state.update({
            "correction_needed": True,
            "correction_attempts": correction_attempts + 1,
            "correction_feedback": correction_feedback
        })
        
        logger.info(f"Self-correction analysis: {correction_feedback}")
        return state
    
    def _analyze_correction_needs(self, response: str, confidence: float, safety: Dict) -> str:
        """Analyze what needs correction / 分析需要修正的内容"""
        issues = []
        
        if confidence < 0.5:
            issues.append("Low confidence - need better retrieval")
        elif confidence < 0.7:
            issues.append("Medium confidence - need better generation")
        
        if not safety.get("is_safe", True):
            issues.append("Safety issues - need safer generation")
        
        if len(response) < 50:
            issues.append("Response too short - need more comprehensive generation")
        
        if not issues:
            issues.append("General improvement needed")
        
        return "; ".join(issues)


class FinalAnswerNode(BaseNode):
    """Final answer node / 最终答案节点"""
    
    def __init__(self):
        super().__init__("FinalAnswer")
    
    def process(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Process final answer / 处理最终答案"""
        generated_response = state.get("generated_response", "")
        reranked_docs = state.get("reranked_docs", [])
        web_results = state.get("web_results", [])
        confidence_score = state.get("confidence_score", 0.0)
        safety_check = state.get("safety_check", {})
        
        logger.info("Preparing final answer")
        
        # Format final answer / 格式化最终答案
        final_answer = self._format_final_answer(generated_response, confidence_score)
        
        # Compile sources / 编译来源
        sources = self._compile_sources(reranked_docs, web_results)
        
        # Prepare metadata / 准备元数据
        metadata = {
            "response_length": len(generated_response),
            "num_sources": len(sources),
            "confidence_level": self._get_confidence_level(confidence_score),
            "safety_status": "safe" if safety_check.get("is_safe", True) else "unsafe"
        }
        
        # Update state / 更新状态
        state.update({
            "final_answer": final_answer,
            "sources": sources,
            "metadata": metadata
        })
        
        logger.info("Final answer prepared")
        return state
    
    def _format_final_answer(self, response: str, confidence: float) -> str:
        """Format final answer / 格式化最终答案"""
        # Add confidence indicator if low / 如果置信度低则添加置信度指示
        if confidence < 0.6:
            response += "\n\n注意：此回答的置信度较低，建议咨询专业医生获取更准确的建议。"
        
        # Add general disclaimer / 添加一般免责声明
        response += "\n\n免责声明：以上建议仅供参考，不能替代专业医疗诊断。如有健康问题，请及时就医。"
        
        return response
    
    def _compile_sources(self, docs: List[Dict], web_results: List[Dict]) -> List[Dict[str, Any]]:
        """Compile sources / 编译来源"""
        sources = []
        
        # Add document sources / 添加文档来源
        for i, doc in enumerate(docs[:3]):
            sources.append({
                "type": "knowledge_base",
                "title": f"医疗知识库文档 {i+1}",
                "content": doc.get("text", "")[:200] + "...",
                "score": doc.get("rerank_score", doc.get("score", 0.0))
            })
        
        # Add web sources / 添加网络来源
        for result in web_results[:2]:
            sources.append({
                "type": "web_search",
                "title": result.get("title", "网络搜索结果"),
                "url": result.get("url", ""),
                "content": result.get("content", "")[:200] + "...",
                "score": result.get("score", 0.0)
            })
        
        return sources
    
    def _get_confidence_level(self, confidence: float) -> str:
        """Get confidence level / 获取置信度等级"""
        if confidence >= 0.8:
            return "high"
        elif confidence >= 0.6:
            return "medium"
        else:
            return "low"
