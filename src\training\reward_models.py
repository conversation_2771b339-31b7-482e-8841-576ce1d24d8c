# -*- coding: utf-8 -*-
"""
Reward Models for GRPO Training / GRPO训练的奖励模型

This module implements various reward models for medical RAG system optimization.
该模块实现医疗RAG系统优化的各种奖励模型。
"""

import re
import numpy as np
from typing import Dict, List, Any, Optional
from sentence_transformers import SentenceTransformer
import openai
from openai import OpenAI

from ..utils import get_logger

logger = get_logger(__name__)


class BaseRewardModel:
    """Base reward model / 基础奖励模型"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize reward model / 初始化奖励模型
        
        Args:
            config: Reward model configuration / 奖励模型配置
        """
        self.config = config
        self.weight = config.get("weight", 1.0)
        self.threshold = config.get("threshold", 0.5)
        
    def calculate_reward(self, prompts, completions, **kwargs) -> List[float]:
        """
        Calculate reward scores / 计算奖励分数
        
        Args:
            prompts: Input prompts / 输入提示
            completions: Generated completions / 生成的完成
            **kwargs: Additional arguments / 额外参数
            
        Returns:
            List of reward scores / 奖励分数列表
        """
        raise NotImplementedError


class MedicalAccuracyReward(BaseRewardModel):
    """Medical accuracy reward model / 医疗准确性奖励模型"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.model_name = config.get("model_name", "gpt-3.5-turbo")
        self.client = OpenAI() if config.get("type") == "llm_based" else None
        
    def calculate_reward(self, prompts, completions, answer=None, **kwargs) -> List[float]:
        """Calculate medical accuracy reward / 计算医疗准确性奖励"""
        scores = []
        
        for prompt, completion in zip(prompts, completions):
            try:
                if self.client:
                    score = self._llm_based_accuracy(prompt, completion, answer)
                else:
                    score = self._rule_based_accuracy(prompt, completion, answer)
                
                scores.append(score * self.weight)
                
            except Exception as e:
                logger.warning(f"Error calculating medical accuracy: {e}")
                scores.append(0.0)
        
        return scores
    
    def _llm_based_accuracy(self, prompt, completion, answer) -> float:
        """LLM-based accuracy evaluation / 基于LLM的准确性评估"""
        query = prompt[0][-1]["content"] if isinstance(prompt[0], list) else str(prompt[0])
        response = completion[0]["content"]
        
        evaluation_prompt = f"""
作为医疗专家，请评估以下医疗回答的准确性：

患者问题：{query}
AI回答：{response}

请从以下维度评分（0-10分）：
1. 医学知识准确性
2. 诊断建议合理性  
3. 治疗方案安全性
4. 专业术语使用正确性

请只返回一个0-10的数字分数。
"""
        
        try:
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": evaluation_prompt}],
                max_tokens=10,
                temperature=0.1
            )
            
            score_text = response.choices[0].message.content.strip()
            score = float(re.findall(r'\d+\.?\d*', score_text)[0])
            return min(score / 10.0, 1.0)  # Normalize to 0-1
            
        except Exception as e:
            logger.warning(f"LLM evaluation failed: {e}")
            return 0.5
    
    def _rule_based_accuracy(self, prompt, completion, answer) -> float:
        """Rule-based accuracy evaluation / 基于规则的准确性评估"""
        response = completion[0]["content"].lower()
        
        # Medical accuracy indicators / 医疗准确性指标
        positive_indicators = [
            "建议", "推荐", "可以", "应该", "需要", "及时", "专业", "医生", "医院",
            "检查", "治疗", "药物", "症状", "诊断", "康复", "预防", "注意"
        ]
        
        negative_indicators = [
            "一定", "绝对", "肯定", "保证", "治愈", "根治", "立即", "马上",
            "不用", "没事", "不要紧", "自己", "随便"
        ]
        
        # Count indicators / 计算指标
        positive_count = sum(1 for indicator in positive_indicators if indicator in response)
        negative_count = sum(1 for indicator in negative_indicators if indicator in response)
        
        # Calculate score / 计算分数
        score = (positive_count - negative_count * 2) / max(len(positive_indicators), 1)
        return max(0.0, min(1.0, score))


class RelevanceReward(BaseRewardModel):
    """Relevance reward model / 相关性奖励模型"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.model_name = config.get("model_name", "BAAI/bge-large-zh-v1.5")
        self.embedding_model = SentenceTransformer(self.model_name)
        
    def calculate_reward(self, prompts, completions, **kwargs) -> List[float]:
        """Calculate relevance reward / 计算相关性奖励"""
        scores = []
        
        for prompt, completion in zip(prompts, completions):
            try:
                query = prompt[0][-1]["content"] if isinstance(prompt[0], list) else str(prompt[0])
                response = completion[0]["content"]
                
                # Calculate semantic similarity / 计算语义相似度
                query_embedding = self.embedding_model.encode([query])
                response_embedding = self.embedding_model.encode([response])
                
                similarity = np.dot(query_embedding[0], response_embedding[0]) / (
                    np.linalg.norm(query_embedding[0]) * np.linalg.norm(response_embedding[0])
                )
                
                # Apply threshold and weight / 应用阈值和权重
                score = max(0.0, similarity - self.threshold) * self.weight
                scores.append(score)
                
            except Exception as e:
                logger.warning(f"Error calculating relevance: {e}")
                scores.append(0.0)
        
        return scores


class SafetyReward(BaseRewardModel):
    """Safety reward model / 安全性奖励模型"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.rules = config.get("rules", [])
        
    def calculate_reward(self, prompts, completions, **kwargs) -> List[float]:
        """Calculate safety reward / 计算安全性奖励"""
        scores = []
        
        for prompt, completion in zip(prompts, completions):
            try:
                response = completion[0]["content"].lower()
                score = 0.0
                
                # Check safety rules / 检查安全规则
                if "no_harmful_advice" in self.rules:
                    score += self._check_no_harmful_advice(response)
                
                if "medical_disclaimer" in self.rules:
                    score += self._check_medical_disclaimer(response)
                
                if "professional_referral" in self.rules:
                    score += self._check_professional_referral(response)
                
                scores.append(score * self.weight)
                
            except Exception as e:
                logger.warning(f"Error calculating safety: {e}")
                scores.append(0.0)
        
        return scores
    
    def _check_no_harmful_advice(self, response: str) -> float:
        """Check for harmful advice / 检查有害建议"""
        harmful_patterns = [
            "自己诊断", "不用看医生", "随便吃药", "没关系", "不要紧",
            "肯定没事", "绝对安全", "保证治愈"
        ]
        
        for pattern in harmful_patterns:
            if pattern in response:
                return -1.0
        
        return 0.5
    
    def _check_medical_disclaimer(self, response: str) -> float:
        """Check for medical disclaimer / 检查医疗免责声明"""
        disclaimer_keywords = [
            "建议咨询", "专业医生", "医院", "就医", "诊断", "仅供参考"
        ]
        
        found_keywords = sum(1 for keyword in disclaimer_keywords if keyword in response)
        return min(1.0, found_keywords / len(disclaimer_keywords))
    
    def _check_professional_referral(self, response: str) -> float:
        """Check for professional referral / 检查专业转诊"""
        referral_keywords = [
            "建议就医", "咨询医生", "专科医生", "医院检查", "及时就诊"
        ]
        
        found_keywords = sum(1 for keyword in referral_keywords if keyword in response)
        return min(0.5, found_keywords / len(referral_keywords))


class CompletenessReward(BaseRewardModel):
    """Completeness reward model / 完整性奖励模型"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.min_length = config.get("min_length", 50)
        self.coverage_keywords = config.get("coverage_keywords", ["症状", "建议", "注意事项"])
        
    def calculate_reward(self, prompts, completions, **kwargs) -> List[float]:
        """Calculate completeness reward / 计算完整性奖励"""
        scores = []
        
        for prompt, completion in zip(prompts, completions):
            try:
                response = completion[0]["content"]
                score = 0.0
                
                # Length check / 长度检查
                if len(response) >= self.min_length:
                    score += 0.5
                
                # Coverage check / 覆盖度检查
                coverage_count = sum(1 for keyword in self.coverage_keywords if keyword in response)
                coverage_score = coverage_count / len(self.coverage_keywords)
                score += coverage_score * 0.5
                
                scores.append(score * self.weight)
                
            except Exception as e:
                logger.warning(f"Error calculating completeness: {e}")
                scores.append(0.0)
        
        return scores


class FormatComplianceReward(BaseRewardModel):
    """Format compliance reward model / 格式合规性奖励模型"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.required_sections = config.get("required_sections", ["分析", "建议"])
        
    def calculate_reward(self, prompts, completions, **kwargs) -> List[float]:
        """Calculate format compliance reward / 计算格式合规性奖励"""
        scores = []
        
        for prompt, completion in zip(prompts, completions):
            try:
                response = completion[0]["content"]
                score = 0.0
                
                # Check required sections / 检查必需部分
                found_sections = sum(1 for section in self.required_sections if section in response)
                score = found_sections / len(self.required_sections)
                
                scores.append(score * self.weight)
                
            except Exception as e:
                logger.warning(f"Error calculating format compliance: {e}")
                scores.append(0.0)
        
        return scores
