#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Model Testing Script / 模型测试脚本

This script tests the trained medical RAG model with various queries.
该脚本使用各种查询测试训练好的医疗RAG模型。
"""

import os
import sys
import argparse
import json
from pathlib import Path
from typing import List, Dict, Any

# Add src to path / 添加src到路径
src_path = os.path.join(os.path.dirname(__file__), '..', 'src')
if src_path not in sys.path:
    sys.path.insert(0, src_path)

try:
    from utils import get_logger, config_manager
    from graph import MedicalRAGGraph
    from generation import MedicalGenerator
    from evaluation import MedicalEvaluator
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all dependencies are installed and the project structure is correct.")
    sys.exit(1)

logger = get_logger(__name__)


def load_test_queries() -> List[Dict[str, str]]:
    """Load test queries / 加载测试查询"""
    return [
        {
            "query": "我最近总是头痛，应该怎么办？",
            "category": "症状咨询",
            "expected_keywords": ["头痛", "休息", "医生", "检查"]
        },
        {
            "query": "发烧了需要注意什么？",
            "category": "症状处理",
            "expected_keywords": ["发烧", "体温", "多喝水", "就医"]
        },
        {
            "query": "咳嗽一直不好，可能是什么原因？",
            "category": "病因分析",
            "expected_keywords": ["咳嗽", "原因", "感冒", "检查"]
        },
        {
            "query": "胃痛应该如何缓解？",
            "category": "治疗建议",
            "expected_keywords": ["胃痛", "缓解", "饮食", "药物"]
        },
        {
            "query": "失眠有什么好的治疗方法？",
            "category": "治疗方法",
            "expected_keywords": ["失眠", "治疗", "睡眠", "放松"]
        },
        {
            "query": "高血压患者饮食需要注意什么？",
            "category": "生活指导",
            "expected_keywords": ["高血压", "饮食", "低盐", "控制"]
        },
        {
            "query": "糖尿病的早期症状有哪些？",
            "category": "疾病症状",
            "expected_keywords": ["糖尿病", "症状", "血糖", "多饮"]
        },
        {
            "query": "孕妇感冒了能吃药吗？",
            "category": "特殊人群",
            "expected_keywords": ["孕妇", "感冒", "药物", "医生"]
        },
        {
            "query": "小孩发烧38.5度需要立即就医吗？",
            "category": "紧急情况",
            "expected_keywords": ["小孩", "发烧", "38.5", "就医"]
        },
        {
            "query": "最新的新冠疫苗有什么副作用？",
            "category": "最新信息",
            "expected_keywords": ["疫苗", "副作用", "新冠", "接种"]
        }
    ]


def test_rag_system(model_path: str = None, output_file: str = None):
    """
    Test RAG system / 测试RAG系统
    
    Args:
        model_path: Path to trained model / 训练模型路径
        output_file: Output file for results / 结果输出文件
    """
    logger.info("Starting RAG system testing...")
    
    # Initialize RAG system / 初始化RAG系统
    try:
        rag_graph = MedicalRAGGraph()
        logger.info("RAG system initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize RAG system: {e}")
        return
    
    # Load test queries / 加载测试查询
    test_queries = load_test_queries()
    logger.info(f"Loaded {len(test_queries)} test queries")
    
    # Test results / 测试结果
    results = []
    
    for i, test_case in enumerate(test_queries):
        query = test_case["query"]
        category = test_case["category"]
        expected_keywords = test_case["expected_keywords"]
        
        logger.info(f"Testing query {i+1}/{len(test_queries)}: {query}")
        
        try:
            # Process query / 处理查询
            result = rag_graph.process_query(query)
            
            # Analyze result / 分析结果
            answer = result["answer"]
            confidence = result["confidence"]
            safety = result["safety"]
            sources = result["sources"]
            metadata = result["metadata"]
            
            # Check for expected keywords / 检查预期关键词
            found_keywords = [kw for kw in expected_keywords if kw in answer]
            keyword_coverage = len(found_keywords) / len(expected_keywords)
            
            # Compile test result / 编译测试结果
            test_result = {
                "query": query,
                "category": category,
                "answer": answer,
                "confidence": confidence,
                "safety_score": safety.get("safety_score", 0.0),
                "is_safe": safety.get("is_safe", True),
                "num_sources": len(sources),
                "keyword_coverage": keyword_coverage,
                "found_keywords": found_keywords,
                "expected_keywords": expected_keywords,
                "response_length": len(answer),
                "metadata": metadata
            }
            
            results.append(test_result)
            
            # Print result / 打印结果
            print(f"\n{'='*60}")
            print(f"Query {i+1}: {query}")
            print(f"Category: {category}")
            print(f"{'='*60}")
            print(f"Answer: {answer}")
            print(f"\nMetrics:")
            print(f"  Confidence: {confidence:.3f}")
            print(f"  Safety Score: {safety.get('safety_score', 0.0):.3f}")
            print(f"  Is Safe: {safety.get('is_safe', True)}")
            print(f"  Sources: {len(sources)}")
            print(f"  Keyword Coverage: {keyword_coverage:.3f}")
            print(f"  Found Keywords: {found_keywords}")
            
            if sources:
                print(f"\nTop Sources:")
                for j, source in enumerate(sources[:2]):
                    print(f"  {j+1}. {source.get('title', 'Unknown')} (Score: {source.get('score', 0.0):.3f})")
            
        except Exception as e:
            logger.error(f"Error processing query '{query}': {e}")
            test_result = {
                "query": query,
                "category": category,
                "error": str(e),
                "answer": "",
                "confidence": 0.0,
                "safety_score": 0.0,
                "is_safe": True,
                "num_sources": 0,
                "keyword_coverage": 0.0,
                "found_keywords": [],
                "expected_keywords": expected_keywords,
                "response_length": 0,
                "metadata": {}
            }
            results.append(test_result)
    
    # Calculate overall statistics / 计算总体统计
    successful_tests = [r for r in results if "error" not in r]
    
    if successful_tests:
        avg_confidence = sum(r["confidence"] for r in successful_tests) / len(successful_tests)
        avg_safety = sum(r["safety_score"] for r in successful_tests) / len(successful_tests)
        avg_keyword_coverage = sum(r["keyword_coverage"] for r in successful_tests) / len(successful_tests)
        avg_response_length = sum(r["response_length"] for r in successful_tests) / len(successful_tests)
        safe_responses = sum(1 for r in successful_tests if r["is_safe"]) / len(successful_tests)
        
        # Print summary / 打印摘要
        print(f"\n{'='*60}")
        print("TESTING SUMMARY / 测试摘要")
        print(f"{'='*60}")
        print(f"Total queries: {len(test_queries)}")
        print(f"Successful: {len(successful_tests)}")
        print(f"Failed: {len(test_queries) - len(successful_tests)}")
        print(f"Average confidence: {avg_confidence:.3f}")
        print(f"Average safety score: {avg_safety:.3f}")
        print(f"Safe responses: {safe_responses:.3f}")
        print(f"Average keyword coverage: {avg_keyword_coverage:.3f}")
        print(f"Average response length: {avg_response_length:.1f} chars")
        print(f"{'='*60}")
        
        # Category analysis / 类别分析
        categories = {}
        for result in successful_tests:
            cat = result["category"]
            if cat not in categories:
                categories[cat] = []
            categories[cat].append(result)
        
        print("\nCATEGORY ANALYSIS / 类别分析:")
        for cat, cat_results in categories.items():
            cat_confidence = sum(r["confidence"] for r in cat_results) / len(cat_results)
            cat_coverage = sum(r["keyword_coverage"] for r in cat_results) / len(cat_results)
            print(f"  {cat}: Confidence={cat_confidence:.3f}, Coverage={cat_coverage:.3f}")
    
    # Save results / 保存结果
    if output_file:
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Results saved to {output_file}")
    
    logger.info("RAG system testing completed")
    return results


def test_generator_only(model_path: str = None):
    """
    Test generator only / 仅测试生成器
    
    Args:
        model_path: Path to trained model / 训练模型路径
    """
    logger.info("Testing generator only...")
    
    try:
        # Initialize generator / 初始化生成器
        generator = MedicalGenerator(model_path)
        generator.load_model()
        
        # Test queries / 测试查询
        test_queries = [
            "我头痛怎么办？",
            "发烧了需要注意什么？",
            "咳嗽的原因有哪些？"
        ]
        
        for query in test_queries:
            result = generator.generate_response(query)
            
            print(f"\nQuery: {query}")
            print(f"Response: {result['response']}")
            print(f"Confidence: {result.get('confidence', {}).get('overall_confidence', 0.0):.3f}")
            print(f"Safety: {result.get('safety', {}).get('is_safe', True)}")
            print("-" * 50)
        
        logger.info("Generator testing completed")
        
    except Exception as e:
        logger.error(f"Generator testing failed: {e}")


def main():
    """Main function / 主函数"""
    parser = argparse.ArgumentParser(description="Test medical RAG model")
    
    parser.add_argument(
        "--model-path",
        type=str,
        default=None,
        help="Path to trained model"
    )
    
    parser.add_argument(
        "--output",
        type=str,
        default="test_results.json",
        help="Output file for test results"
    )
    
    parser.add_argument(
        "--generator-only",
        action="store_true",
        help="Test generator only (not full RAG system)"
    )
    
    args = parser.parse_args()
    
    try:
        if args.generator_only:
            test_generator_only(args.model_path)
        else:
            test_rag_system(args.model_path, args.output)
    except Exception as e:
        logger.error(f"Error in model testing: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
