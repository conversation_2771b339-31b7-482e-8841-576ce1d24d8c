# -*- coding: utf-8 -*-
"""
Web Search Module / 网络搜索模块

This module implements web search capabilities for retrieving latest medical information.
该模块实现网络搜索功能，用于检索最新的医疗信息。
"""

import os
import requests
from typing import List, Dict, Optional, Any
from datetime import datetime
from ..utils import get_logger, get_config_manager, clean_text

logger = get_logger(__name__)


class MedicalWebSearcher:
    """Medical web searcher for latest information / 用于获取最新信息的医疗网络搜索器"""
    
    def __init__(self, api_key: str = None):
        """
        Initialize medical web searcher / 初始化医疗网络搜索器
        
        Args:
            api_key: Tavily API key / Tavily API密钥
        """
        config_manager = get_config_manager()
        self.web_search_config = config_manager.web_search
        self.api_key = api_key or os.getenv("TAVILY_API_KEY")
        self.enabled = self.web_search_config.get("enabled", True)
        self.max_results = self.web_search_config.get("max_results", 5)
        self.search_depth = self.web_search_config.get("search_depth", "advanced")
        self.time_range = self.web_search_config.get("time_range", "month")
        self.domains_whitelist = self.web_search_config.get("domains_whitelist", [])
        
        if not self.api_key and self.enabled:
            logger.warning("Tavily API key not found. Web search will be disabled.")
            self.enabled = False
        
        logger.info(f"Web searcher initialized. Enabled: {self.enabled}")
    
    def search(self, query: str, max_results: int = None, 
               include_latest: bool = True) -> List[Dict[str, Any]]:
        """
        Search for medical information on the web / 在网络上搜索医疗信息
        
        Args:
            query: Search query / 搜索查询
            max_results: Maximum number of results / 最大结果数
            include_latest: Whether to prioritize latest information / 是否优先最新信息
            
        Returns:
            List of search results / 搜索结果列表
        """
        if not self.enabled:
            logger.warning("Web search is disabled")
            return []
        
        max_results = max_results or self.max_results
        
        # Enhance query for medical context / 为医疗上下文增强查询
        enhanced_query = self._enhance_medical_query(query, include_latest)
        
        logger.info(f"Searching web for: {enhanced_query}")
        
        try:
            # Use Tavily API for web search / 使用Tavily API进行网络搜索
            results = self._tavily_search(enhanced_query, max_results)
            
            # Filter and process results / 过滤和处理结果
            processed_results = self._process_search_results(results, query)
            
            logger.info(f"Found {len(processed_results)} relevant web results")
            return processed_results
            
        except Exception as e:
            logger.error(f"Web search failed: {e}")
            return []
    
    def _enhance_medical_query(self, query: str, include_latest: bool = True) -> str:
        """
        Enhance query for better medical search results / 增强查询以获得更好的医疗搜索结果
        
        Args:
            query: Original query / 原始查询
            include_latest: Whether to include latest information keywords / 是否包含最新信息关键词
            
        Returns:
            Enhanced query / 增强后的查询
        """
        enhanced_query = query
        
        # Add medical context if not present / 如果不存在则添加医疗上下文
        medical_keywords = ["医疗", "健康", "疾病", "症状", "治疗", "药物"]
        if not any(keyword in query for keyword in medical_keywords):
            enhanced_query = f"医疗健康 {query}"
        
        # Add latest information keywords if requested / 如果需要则添加最新信息关键词
        if include_latest:
            latest_keywords = ["2024", "2025", "最新", "新研究", "最近"]
            if not any(keyword in query for keyword in latest_keywords):
                enhanced_query = f"{enhanced_query} 2025年最新"
        
        return enhanced_query
    
    def _tavily_search(self, query: str, max_results: int) -> List[Dict]:
        """
        Perform search using Tavily API / 使用Tavily API执行搜索
        
        Args:
            query: Search query / 搜索查询
            max_results: Maximum number of results / 最大结果数
            
        Returns:
            Raw search results / 原始搜索结果
        """
        url = "https://api.tavily.com/search"
        
        payload = {
            "api_key": self.api_key,
            "query": query,
            "search_depth": self.search_depth,
            "max_results": max_results,
            "include_answer": True,
            "include_raw_content": False,
            "include_images": False
        }
        
        # Add time range if specified / 如果指定则添加时间范围
        if self.time_range:
            payload["time_range"] = self.time_range
        
        # Add domain filtering if specified / 如果指定则添加域名过滤
        if self.domains_whitelist:
            payload["include_domains"] = self.domains_whitelist
        
        response = requests.post(url, json=payload, timeout=30)
        response.raise_for_status()
        
        return response.json().get("results", [])
    
    def _process_search_results(self, results: List[Dict], 
                              original_query: str) -> List[Dict[str, Any]]:
        """
        Process and filter search results / 处理和过滤搜索结果
        
        Args:
            results: Raw search results / 原始搜索结果
            original_query: Original search query / 原始搜索查询
            
        Returns:
            Processed search results / 处理后的搜索结果
        """
        processed_results = []
        
        for result in results:
            try:
                # Extract and clean content / 提取和清理内容
                title = result.get("title", "")
                content = result.get("content", "")
                url = result.get("url", "")
                score = result.get("score", 0.0)
                
                # Clean and validate content / 清理和验证内容
                if not title or not content or len(content) < 50:
                    continue
                
                cleaned_content = clean_text(content)
                
                # Check relevance to medical query / 检查与医疗查询的相关性
                if not self._is_medically_relevant(cleaned_content, original_query):
                    continue
                
                processed_result = {
                    "id": f"web_{len(processed_results)}",
                    "title": title,
                    "content": cleaned_content,
                    "url": url,
                    "score": score,
                    "source": "web_search",
                    "timestamp": datetime.now().isoformat(),
                    "metadata": {
                        "type": "web_result",
                        "domain": self._extract_domain(url),
                        "content_length": len(cleaned_content),
                        "search_query": original_query
                    }
                }
                
                processed_results.append(processed_result)
                
            except Exception as e:
                logger.warning(f"Error processing search result: {e}")
                continue
        
        return processed_results
    
    def _is_medically_relevant(self, content: str, query: str) -> bool:
        """
        Check if content is medically relevant / 检查内容是否与医疗相关
        
        Args:
            content: Content to check / 要检查的内容
            query: Original query / 原始查询
            
        Returns:
            Whether content is medically relevant / 内容是否与医疗相关
        """
        # Medical relevance keywords / 医疗相关性关键词
        medical_keywords = [
            "医疗", "健康", "疾病", "症状", "治疗", "药物", "医生", "医院",
            "诊断", "康复", "预防", "保健", "临床", "病理", "药理",
            "患者", "病人", "医学", "护理", "手术", "检查", "化验"
        ]
        
        content_lower = content.lower()
        
        # Check for medical keywords / 检查医疗关键词
        medical_score = sum(1 for keyword in medical_keywords if keyword in content_lower)
        
        # Check for query terms / 检查查询术语
        query_terms = query.lower().split()
        query_score = sum(1 for term in query_terms if term in content_lower)
        
        # Content is relevant if it has medical context and query relevance / 如果有医疗上下文和查询相关性则内容相关
        return medical_score >= 2 and query_score >= 1
    
    def _extract_domain(self, url: str) -> str:
        """
        Extract domain from URL / 从URL提取域名
        
        Args:
            url: URL string / URL字符串
            
        Returns:
            Domain name / 域名
        """
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            return parsed.netloc
        except Exception:
            return "unknown"
    
    def search_medical_guidelines(self, condition: str) -> List[Dict[str, Any]]:
        """
        Search for medical guidelines for a specific condition / 搜索特定疾病的医疗指南
        
        Args:
            condition: Medical condition / 医疗状况
            
        Returns:
            List of guideline results / 指南结果列表
        """
        query = f"{condition} 医疗指南 临床指南 诊疗规范 2025"
        return self.search(query, max_results=3, include_latest=True)
    
    def search_drug_information(self, drug_name: str) -> List[Dict[str, Any]]:
        """
        Search for drug information / 搜索药物信息
        
        Args:
            drug_name: Name of the drug / 药物名称
            
        Returns:
            List of drug information results / 药物信息结果列表
        """
        query = f"{drug_name} 药物信息 用法用量 副作用 注意事项"
        return self.search(query, max_results=3, include_latest=True)
    
    def search_latest_research(self, topic: str) -> List[Dict[str, Any]]:
        """
        Search for latest medical research / 搜索最新医学研究
        
        Args:
            topic: Research topic / 研究主题
            
        Returns:
            List of research results / 研究结果列表
        """
        query = f"{topic} 最新研究 医学研究 临床试验 2024 2025"
        return self.search(query, max_results=5, include_latest=True)
    
    def is_enabled(self) -> bool:
        """
        Check if web search is enabled / 检查网络搜索是否启用
        
        Returns:
            Whether web search is enabled / 网络搜索是否启用
        """
        return self.enabled
    
    def get_search_stats(self) -> Dict[str, Any]:
        """
        Get web search statistics / 获取网络搜索统计信息
        
        Returns:
            Search statistics / 搜索统计信息
        """
        return {
            "enabled": self.enabled,
            "api_key_configured": bool(self.api_key),
            "max_results": self.max_results,
            "search_depth": self.search_depth,
            "time_range": self.time_range,
            "domains_whitelist": self.domains_whitelist,
            "fallback_enabled": self.web_search_config.get("fallback_enabled", True)
        }
