# Development override for Docker Compose
# Docker Compose开发环境覆盖配置

version: '3.8'

services:
  grag-app:
    build:
      target: development
    volumes:
      # Mount source code for hot reload
      - ..:/app
      - ../data:/app/data
      - ../models:/app/models
      - ../logs:/app/logs
      - ../notebooks:/app/notebooks
    ports:
      - "8501:8501"
      - "8888:8888"  # Jupyter
    environment:
      - STREAMLIT_SERVER_PORT=8501
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0
      - JUPYTER_ENABLE_LAB=yes
      - LOG_LEVEL=DEBUG
      - PYTHONPATH=/app
    command: >
      bash -c "
        echo 'Starting development environment...' &&
        jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root --NotebookApp.token='' --NotebookApp.password='' &
        streamlit run src/frontend/app.py --server.port=8501 --server.address=0.0.0.0 --server.runOnSave=true &
        wait
      "

  # Development database for testing
  dev-db:
    image: postgres:15-alpine
    container_name: grag-dev-db
    environment:
      - POSTGRES_DB=grag_dev
      - POSTGRES_USER=grag_user
      - POSTGRES_PASSWORD=grag_pass
    ports:
      - "5432:5432"
    volumes:
      - dev_db_data:/var/lib/postgresql/data
    networks:
      - grag-network

volumes:
  dev_db_data:
    driver: local
