#!/usr/bin/env python3
"""
测试Docker配置的脚本
Test script for Docker configuration
"""

import os
import sys
import yaml
import subprocess
from pathlib import Path

def test_docker_files():
    """测试Docker文件是否存在且格式正确"""
    print("🔍 检查Docker文件...")
    
    docker_dir = Path(__file__).parent
    required_files = [
        'Dockerfile',
        'docker-compose.yml',
        'docker-compose.dev.yml',
        'docker-compose.prod.yml',
        '.dockerignore',
        'README.md'
    ]
    
    missing_files = []
    for file in required_files:
        file_path = docker_dir / file
        if not file_path.exists():
            missing_files.append(file)
        else:
            print(f"✅ {file} - 存在")
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    
    return True

def test_docker_compose_syntax():
    """测试docker-compose文件语法"""
    print("\n🔍 检查docker-compose语法...")
    
    docker_dir = Path(__file__).parent
    compose_files = [
        'docker-compose.yml',
        'docker-compose.dev.yml',
        'docker-compose.prod.yml'
    ]
    
    for compose_file in compose_files:
        try:
            with open(docker_dir / compose_file, 'r', encoding='utf-8') as f:
                yaml.safe_load(f)
            print(f"✅ {compose_file} - 语法正确")
        except yaml.YAMLError as e:
            print(f"❌ {compose_file} - YAML语法错误: {e}")
            return False
        except Exception as e:
            print(f"❌ {compose_file} - 读取错误: {e}")
            return False
    
    return True

def test_docker_command():
    """测试Docker命令是否可用"""
    print("\n🔍 检查Docker环境...")
    
    try:
        # 检查Docker是否安装
        result = subprocess.run(['docker', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"✅ Docker版本: {result.stdout.strip()}")
        
        # 检查Docker Compose是否可用
        result = subprocess.run(['docker-compose', '--version'], 
                              capture_output=True, text=True, check=True)
        print(f"✅ Docker Compose版本: {result.stdout.strip()}")
        
        return True
    except subprocess.CalledProcessError:
        print("❌ Docker或Docker Compose未安装或不可用")
        return False
    except FileNotFoundError:
        print("❌ Docker命令未找到，请确保Docker已安装")
        return False

def test_project_structure():
    """测试项目结构是否正确"""
    print("\n🔍 检查项目结构...")
    
    project_root = Path(__file__).parent.parent
    required_dirs = [
        'src',
        'configs',
        'data',
        'models',
        'scripts',
        'logs'
    ]
    
    required_files = [
        'requirements.txt',
        'Makefile'
    ]
    
    missing_items = []
    
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        if not dir_path.exists():
            missing_items.append(f"目录: {dir_name}")
        else:
            print(f"✅ 目录 {dir_name} - 存在")
    
    for file_name in required_files:
        file_path = project_root / file_name
        if not file_path.exists():
            missing_items.append(f"文件: {file_name}")
        else:
            print(f"✅ 文件 {file_name} - 存在")
    
    if missing_items:
        print(f"❌ 缺少项目文件/目录: {', '.join(missing_items)}")
        return False
    
    return True

def test_dockerfile_syntax():
    """测试Dockerfile语法"""
    print("\n🔍 检查Dockerfile语法...")
    
    docker_dir = Path(__file__).parent
    dockerfile_path = docker_dir / 'Dockerfile'
    
    try:
        with open(dockerfile_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 基本语法检查
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if line and not line.startswith('#'):
                # 检查是否以有效的Docker指令开始
                valid_instructions = [
                    'FROM', 'RUN', 'CMD', 'LABEL', 'EXPOSE', 'ENV',
                    'ADD', 'COPY', 'ENTRYPOINT', 'VOLUME', 'USER',
                    'WORKDIR', 'ARG', 'ONBUILD', 'STOPSIGNAL',
                    'HEALTHCHECK', 'SHELL'
                ]
                
                if not any(line.upper().startswith(inst) for inst in valid_instructions):
                    # 可能是多行指令的续行
                    if not line.startswith('\\') and not line.endswith('\\'):
                        print(f"⚠️  第{i}行可能有语法问题: {line}")
        
        print("✅ Dockerfile - 基本语法检查通过")
        return True
        
    except Exception as e:
        print(f"❌ Dockerfile语法检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🐳 GRAG Docker配置测试")
    print("=" * 50)
    
    tests = [
        ("Docker文件检查", test_docker_files),
        ("Docker Compose语法检查", test_docker_compose_syntax),
        ("Dockerfile语法检查", test_dockerfile_syntax),
        ("Docker环境检查", test_docker_command),
        ("项目结构检查", test_project_structure),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有Docker配置测试通过！")
        print("\n💡 下一步:")
        print("1. 设置环境变量: cp .env.example .env")
        print("2. 启动开发环境: make docker-dev")
        print("3. 或启动生产环境: make docker-prod")
        return 0
    else:
        print("⚠️  部分测试失败，请检查配置")
        return 1

if __name__ == "__main__":
    sys.exit(main())
