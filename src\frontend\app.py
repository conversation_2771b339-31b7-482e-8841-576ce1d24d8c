# -*- coding: utf-8 -*-
"""
Streamlit Frontend Application / Streamlit前端应用

This module implements the main Streamlit application for the medical RAG system.
该模块实现医疗RAG系统的主要Streamlit应用。
"""

import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from typing import Dict, List, Any, Optional
import time
import json
from datetime import datetime

# Import system components / 导入系统组件
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.utils import get_logger, config_manager
from src.graph import MedicalRAGGraph

logger = get_logger(__name__)


class MedicalRAGApp:
    """Medical RAG Streamlit application / 医疗RAG Streamlit应用"""
    
    def __init__(self):
        """Initialize the application / 初始化应用"""
        self.config = config_manager.frontend
        self.rag_graph = None
        self._initialize_session_state()
        self._setup_page_config()
        
    def _initialize_session_state(self):
        """Initialize session state / 初始化会话状态"""
        if "messages" not in st.session_state:
            st.session_state.messages = []
        
        if "conversation_history" not in st.session_state:
            st.session_state.conversation_history = []
        
        if "system_stats" not in st.session_state:
            st.session_state.system_stats = {
                "total_queries": 0,
                "avg_confidence": 0.0,
                "avg_response_time": 0.0
            }
    
    def _setup_page_config(self):
        """Setup page configuration / 设置页面配置"""
        st.set_page_config(
            page_title=self.config.title,
            page_icon="🏥",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        # Custom CSS / 自定义CSS
        st.markdown("""
        <style>
        .main-header {
            font-size: 2.5rem;
            color: #1f77b4;
            text-align: center;
            margin-bottom: 2rem;
        }
        .subtitle {
            font-size: 1.2rem;
            color: #666;
            text-align: center;
            margin-bottom: 3rem;
        }
        .confidence-high {
            color: #28a745;
            font-weight: bold;
        }
        .confidence-medium {
            color: #ffc107;
            font-weight: bold;
        }
        .confidence-low {
            color: #dc3545;
            font-weight: bold;
        }
        .source-card {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            border-left: 4px solid #1f77b4;
            margin: 0.5rem 0;
        }
        </style>
        """, unsafe_allow_html=True)
    
    def _load_rag_system(self):
        """Load RAG system / 加载RAG系统"""
        if self.rag_graph is None:
            with st.spinner("正在加载医疗RAG系统..."):
                try:
                    self.rag_graph = MedicalRAGGraph()
                    st.success("系统加载成功！")
                except Exception as e:
                    st.error(f"系统加载失败: {e}")
                    logger.error(f"Failed to load RAG system: {e}")
                    return False
        return True
    
    def render_header(self):
        """Render application header / 渲染应用标题"""
        st.markdown(f'<h1 class="main-header">{self.config.title}</h1>', unsafe_allow_html=True)
        st.markdown(f'<p class="subtitle">{self.config.subtitle}</p>', unsafe_allow_html=True)
        st.markdown(f'<p style="text-align: center; color: #888;">{self.config.description}</p>', unsafe_allow_html=True)
    
    def render_sidebar(self):
        """Render sidebar / 渲染侧边栏"""
        with st.sidebar:
            st.header("🔧 系统设置")
            
            # System status / 系统状态
            st.subheader("📊 系统状态")
            if self.rag_graph:
                st.success("✅ 系统已就绪")
            else:
                st.warning("⚠️ 系统未加载")
            
            # Statistics / 统计信息
            stats = st.session_state.system_stats
            col1, col2 = st.columns(2)
            with col1:
                st.metric("总查询数", stats["total_queries"])
            with col2:
                st.metric("平均置信度", f"{stats['avg_confidence']:.2f}")
            
            st.metric("平均响应时间", f"{stats['avg_response_time']:.2f}s")
            
            # Settings / 设置
            st.subheader("⚙️ 生成设置")
            
            max_tokens = st.slider("最大生成长度", 100, 1000, 500)
            temperature = st.slider("生成温度", 0.1, 1.0, 0.7, 0.1)
            show_sources = st.checkbox("显示来源", value=True)
            show_confidence = st.checkbox("显示置信度", value=True)
            
            # Clear history / 清除历史
            if st.button("🗑️ 清除对话历史"):
                st.session_state.messages = []
                st.session_state.conversation_history = []
                st.rerun()
            
            return {
                "max_tokens": max_tokens,
                "temperature": temperature,
                "show_sources": show_sources,
                "show_confidence": show_confidence
            }
    
    def render_chat_interface(self, settings: Dict[str, Any]):
        """Render chat interface / 渲染聊天界面"""
        # Display chat messages / 显示聊天消息
        for message in st.session_state.messages:
            with st.chat_message(message["role"]):
                st.markdown(message["content"])
                
                # Show additional info for assistant messages / 为助手消息显示额外信息
                if message["role"] == "assistant" and "metadata" in message:
                    self._render_message_metadata(message["metadata"], settings)
        
        # Chat input / 聊天输入
        if prompt := st.chat_input("请输入您的医疗问题..."):
            # Add user message / 添加用户消息
            st.session_state.messages.append({"role": "user", "content": prompt})
            
            with st.chat_message("user"):
                st.markdown(prompt)
            
            # Generate response / 生成回复
            with st.chat_message("assistant"):
                self._generate_and_display_response(prompt, settings)
    
    def _generate_and_display_response(self, query: str, settings: Dict[str, Any]):
        """Generate and display response / 生成并显示回复"""
        if not self._load_rag_system():
            st.error("系统未就绪，无法处理查询")
            return
        
        # Show loading / 显示加载
        with st.spinner("正在分析您的问题并生成回复..."):
            start_time = time.time()
            
            try:
                # Process query / 处理查询
                result = self.rag_graph.process_query(
                    query,
                    max_tokens=settings["max_tokens"],
                    temperature=settings["temperature"]
                )
                
                response_time = time.time() - start_time
                
                # Display response / 显示回复
                st.markdown(result["answer"])
                
                # Prepare metadata / 准备元数据
                metadata = {
                    "confidence": result["confidence"],
                    "safety": result["safety"],
                    "sources": result["sources"],
                    "response_time": response_time,
                    "metadata": result["metadata"]
                }
                
                # Add to session state / 添加到会话状态
                st.session_state.messages.append({
                    "role": "assistant",
                    "content": result["answer"],
                    "metadata": metadata
                })
                
                # Update statistics / 更新统计
                self._update_statistics(result["confidence"], response_time)
                
                # Show metadata / 显示元数据
                self._render_message_metadata(metadata, settings)
                
            except Exception as e:
                st.error(f"生成回复时出现错误: {e}")
                logger.error(f"Error generating response: {e}")
    
    def _render_message_metadata(self, metadata: Dict[str, Any], settings: Dict[str, Any]):
        """Render message metadata / 渲染消息元数据"""
        # Show confidence / 显示置信度
        if settings["show_confidence"]:
            confidence = metadata.get("confidence", 0.0)
            self._render_confidence_score(confidence)
        
        # Show sources / 显示来源
        if settings["show_sources"]:
            sources = metadata.get("sources", [])
            if sources:
                self._render_sources(sources)
        
        # Show additional metadata / 显示额外元数据
        with st.expander("📊 详细信息"):
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("响应时间", f"{metadata.get('response_time', 0):.2f}s")
            
            with col2:
                safety = metadata.get("safety", {})
                safety_status = "安全" if safety.get("is_safe", True) else "需注意"
                st.metric("安全性", safety_status)
            
            with col3:
                query_metadata = metadata.get("metadata", {})
                query_type = query_metadata.get("query_type", "未知")
                st.metric("查询类型", query_type)
            
            # Show safety details / 显示安全详情
            if not safety.get("is_safe", True):
                st.warning("⚠️ 安全提醒:")
                for warning in safety.get("warnings", []):
                    st.write(f"• {warning}")
    
    def _render_confidence_score(self, confidence: float):
        """Render confidence score / 渲染置信度分数"""
        # Determine confidence level / 确定置信度等级
        if confidence >= 0.8:
            level = "高"
            css_class = "confidence-high"
            color = "#28a745"
        elif confidence >= 0.6:
            level = "中等"
            css_class = "confidence-medium"
            color = "#ffc107"
        else:
            level = "较低"
            css_class = "confidence-low"
            color = "#dc3545"
        
        # Create gauge chart / 创建仪表盘图表
        fig = go.Figure(go.Indicator(
            mode="gauge+number",
            value=confidence,
            domain={'x': [0, 1], 'y': [0, 1]},
            title={'text': "置信度"},
            gauge={
                'axis': {'range': [None, 1]},
                'bar': {'color': color},
                'steps': [
                    {'range': [0, 0.6], 'color': "lightgray"},
                    {'range': [0.6, 0.8], 'color': "yellow"},
                    {'range': [0.8, 1], 'color': "lightgreen"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 0.9
                }
            }
        ))
        
        fig.update_layout(height=200, margin=dict(l=20, r=20, t=40, b=20))
        
        col1, col2 = st.columns([1, 2])
        with col1:
            st.plotly_chart(fig, use_container_width=True)
        with col2:
            st.markdown(f'<p class="{css_class}">置信度等级: {level} ({confidence:.2f})</p>', unsafe_allow_html=True)
            
            if confidence < 0.6:
                st.warning("置信度较低，建议咨询专业医生")
    
    def _render_sources(self, sources: List[Dict[str, Any]]):
        """Render sources / 渲染来源"""
        st.subheader("📚 参考来源")
        
        for i, source in enumerate(sources):
            source_type = source.get("type", "unknown")
            title = source.get("title", f"来源 {i+1}")
            content = source.get("content", "")
            score = source.get("score", 0.0)
            
            # Source icon / 来源图标
            icon = "📖" if source_type == "knowledge_base" else "🌐"
            
            with st.expander(f"{icon} {title} (相关度: {score:.2f})"):
                st.write(content)
                
                if source_type == "web_search" and "url" in source:
                    st.markdown(f"[查看原文]({source['url']})")
    
    def _update_statistics(self, confidence: float, response_time: float):
        """Update system statistics / 更新系统统计"""
        stats = st.session_state.system_stats
        
        # Update counters / 更新计数器
        stats["total_queries"] += 1
        
        # Update averages / 更新平均值
        n = stats["total_queries"]
        stats["avg_confidence"] = ((n - 1) * stats["avg_confidence"] + confidence) / n
        stats["avg_response_time"] = ((n - 1) * stats["avg_response_time"] + response_time) / n
    
    def render_analytics_tab(self):
        """Render analytics tab / 渲染分析标签页"""
        st.header("📊 系统分析")
        
        if not st.session_state.conversation_history:
            st.info("暂无对话数据")
            return
        
        # Confidence distribution / 置信度分布
        confidences = [msg.get("metadata", {}).get("confidence", 0) 
                      for msg in st.session_state.messages 
                      if msg["role"] == "assistant"]
        
        if confidences:
            fig = px.histogram(
                x=confidences,
                nbins=20,
                title="置信度分布",
                labels={"x": "置信度", "y": "频次"}
            )
            st.plotly_chart(fig, use_container_width=True)
        
        # Response time trend / 响应时间趋势
        response_times = [msg.get("metadata", {}).get("response_time", 0) 
                         for msg in st.session_state.messages 
                         if msg["role"] == "assistant"]
        
        if response_times:
            fig = px.line(
                y=response_times,
                title="响应时间趋势",
                labels={"x": "查询序号", "y": "响应时间 (秒)"}
            )
            st.plotly_chart(fig, use_container_width=True)
    
    def run(self):
        """Run the application / 运行应用"""
        # Render header / 渲染标题
        self.render_header()
        
        # Create tabs / 创建标签页
        tab1, tab2 = st.tabs(["💬 医疗咨询", "📊 系统分析"])
        
        with tab1:
            # Render sidebar / 渲染侧边栏
            settings = self.render_sidebar()
            
            # Render chat interface / 渲染聊天界面
            self.render_chat_interface(settings)
        
        with tab2:
            # Render analytics / 渲染分析
            self.render_analytics_tab()


def main():
    """Main function / 主函数"""
    try:
        app = MedicalRAGApp()
        app.run()
    except Exception as e:
        st.error(f"应用启动失败: {e}")
        logger.error(f"Application startup failed: {e}")


if __name__ == "__main__":
    main()
