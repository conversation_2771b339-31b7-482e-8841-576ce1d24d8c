#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simplified Data Download Script / 简化数据下载脚本

This script downloads and preprocesses the medical dataset without creating vector store.
该脚本下载和预处理医疗数据集，但不创建向量存储。
"""

import sys
import os
from pathlib import Path

# Add project root and src to path
project_root = Path(__file__).parent.parent
src_path = project_root / "src"

# Change to project root directory
os.chdir(project_root)

# Add both paths to sys.path
for path in [str(project_root), str(src_path)]:
    if path not in sys.path:
        sys.path.insert(0, path)

# Set PYTHONPATH environment variable
os.environ['PYTHONPATH'] = str(src_path) + os.pathsep + os.environ.get('PYTHONPATH', '')

# Import modules with better error handling
def import_modules():
    """Import required modules with fallback mechanisms"""
    try:
        # Try absolute imports first
        sys.path.insert(0, str(src_path))

        from data_processing import MedicalDataset<PERSON>oader, MedicalTextPreprocessor, MedicalDataAugmenter
        from utils import get_logger, get_config_manager, set_seed

        return MedicalDatasetLoader, MedicalTextPreprocessor, MedicalDataAugmenter, get_logger, get_config_manager, set_seed

    except ImportError as e1:
        print(f"First import attempt failed: {e1}")
        try:
            # Try with src prefix
            from src.data_processing import MedicalDatasetLoader, MedicalTextPreprocessor, MedicalDataAugmenter
            from src.utils import get_logger, get_config_manager, set_seed

            return MedicalDatasetLoader, MedicalTextPreprocessor, MedicalDataAugmenter, get_logger, get_config_manager, set_seed

        except ImportError as e2:
            print(f"Second import attempt failed: {e2}")
            print("Please ensure all dependencies are installed and the project structure is correct.")
            sys.exit(1)

# Import all required modules
MedicalDatasetLoader, MedicalTextPreprocessor, MedicalDataAugmenter, get_logger, get_config_manager, set_seed = import_modules()

logger = get_logger(__name__)


def main():
    """Main function to download and process data (without vector store) / 下载和处理数据的主函数（不包括向量存储）"""
    try:
        # Set random seed for reproducibility / 设置随机种子以确保可重现性
        config_manager = get_config_manager()
        set_seed(config_manager.environment.seed)
        
        logger.info("Starting simplified data download and processing...")
        
        # Initialize components / 初始化组件
        dataset_loader = MedicalDatasetLoader()
        preprocessor = MedicalTextPreprocessor()
        augmenter = MedicalDataAugmenter(augmentation_ratio=0.1)
        
        # Step 1: Load raw dataset / 步骤1：加载原始数据集
        logger.info("Step 1: Loading raw dataset...")
        raw_dataset = dataset_loader.load_raw_dataset()
        
        # Step 2: Preprocess data / 步骤2：预处理数据
        logger.info("Step 2: Preprocessing data...")
        processed_data = dataset_loader.preprocess_data(raw_dataset)
        
        # Step 3: Split dataset / 步骤3：分割数据集
        logger.info("Step 3: Splitting dataset...")
        train_data, val_data, test_data = dataset_loader.split_dataset(processed_data)
        
        # Step 4: Data augmentation (optional) / 步骤4：数据增强（可选）
        if config_manager.get("data.augmentation.enabled", False):
            logger.info("Step 4: Applying data augmentation...")
            train_data = augmenter.augment_dataset(train_data)
        
        # Step 5: Save processed data / 步骤5：保存处理后的数据
        logger.info("Step 5: Saving processed data...")
        dataset_loader.save_processed_data(train_data, val_data, test_data)
        
        # Step 6: Prepare training data / 步骤6：准备训练数据
        logger.info("Step 6: Preparing training data...")
        sft_data = dataset_loader.prepare_sft_data(train_data)
        grpo_data = dataset_loader.prepare_grpo_data(train_data)
        
        # Save training data / 保存训练数据
        dataset_loader.save_processed_data(
            sft_data, 
            dataset_loader.prepare_sft_data(val_data), 
            dataset_loader.prepare_sft_data(test_data),
            output_dir="data/processed/sft"
        )
        
        dataset_loader.save_processed_data(
            grpo_data,
            dataset_loader.prepare_grpo_data(val_data),
            dataset_loader.prepare_grpo_data(test_data), 
            output_dir="data/processed/grpo"
        )
        
        # Step 7: Create knowledge base / 步骤7：创建知识库
        logger.info("Step 7: Creating knowledge base...")
        knowledge_base = dataset_loader.create_knowledge_base(train_data + val_data)
        dataset_loader.save_knowledge_base(knowledge_base)
        
        # Print summary / 打印摘要
        logger.info("=" * 60)
        logger.info("DATA PROCESSING COMPLETED SUCCESSFULLY!")
        logger.info("=" * 60)
        logger.info(f"Training samples: {len(train_data)}")
        logger.info(f"Validation samples: {len(val_data)}")
        logger.info(f"Test samples: {len(test_data)}")
        logger.info(f"Knowledge base items: {len(knowledge_base)}")
        logger.info("=" * 60)
        
        # Print next steps / 打印下一步
        logger.info("NEXT STEPS:")
        logger.info("1. Create vector store: python scripts/fix_vector_store.py")
        logger.info("2. Run SFT training: python scripts/train_sft.py")
        logger.info("3. Run GRPO training: python scripts/train_grpo.py") 
        logger.info("4. Start frontend: streamlit run src/frontend/app.py")
        logger.info("=" * 60)
        
        logger.info("Note: Vector store creation has been separated to avoid hanging issues.")
        logger.info("Run the fix_vector_store.py script next to create the vector store.")
        
    except Exception as e:
        logger.error(f"Error in data processing: {e}")
        raise


if __name__ == "__main__":
    main()
