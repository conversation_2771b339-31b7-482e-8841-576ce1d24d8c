# -*- coding: utf-8 -*-
"""
Data Augmentation Module / 数据增强模块

This module provides data augmentation techniques for medical text data.
该模块为医疗文本数据提供数据增强技术。
"""

import random
import re
from typing import List, Dict, Any, Optional, Tuple
from ..utils import get_logger, segment_chinese_text

logger = get_logger(__name__)


class MedicalDataAugmenter:
    """Medical data augmenter / 医疗数据增强器"""
    
    def __init__(self, augmentation_ratio: float = 0.1):
        """
        Initialize data augmenter / 初始化数据增强器
        
        Args:
            augmentation_ratio: Ratio of data to augment / 数据增强比例
        """
        self.augmentation_ratio = augmentation_ratio
        self._load_synonym_dict()
        logger.info(f"Medical data augmenter initialized with ratio: {augmentation_ratio}")
    
    def _load_synonym_dict(self):
        """Load medical synonym dictionary / 加载医疗同义词词典"""
        # Medical synonyms for data augmentation / 用于数据增强的医疗同义词
        self.synonyms = {
            # Symptoms / 症状
            "头痛": ["头疼", "脑袋疼", "头部疼痛"],
            "发热": ["发烧", "体温升高", "发高烧"],
            "咳嗽": ["咳", "干咳", "咳痰"],
            "腹痛": ["肚子疼", "腹部疼痛", "胃疼"],
            "胸痛": ["胸部疼痛", "胸闷痛", "心口疼"],
            "呼吸困难": ["气喘", "喘不过气", "呼吸急促"],
            "恶心": ["想吐", "反胃", "恶心想吐"],
            "呕吐": ["吐", "呕", "反胃吐"],
            "腹泻": ["拉肚子", "腹泻", "大便稀"],
            "便秘": ["大便干燥", "排便困难", "便干"],
            
            # Common expressions / 常见表达
            "怎么办": ["如何处理", "该怎么做", "怎么治疗"],
            "什么原因": ["为什么", "原因是什么", "怎么回事"],
            "需要注意": ["注意什么", "要注意", "注意事项"],
            "是否需要": ["需要吗", "要不要", "是否要"],
            
            # Medical terms / 医疗术语
            "医生": ["大夫", "医师", "专家"],
            "医院": ["医疗机构", "诊所", "卫生院"],
            "药物": ["药品", "药", "medication"],
            "治疗": ["医治", "诊治", "therapy"],
            "检查": ["检验", "化验", "examination"],
        }
    
    def augment_dataset(self, data: List[Dict[str, Any]], 
                       methods: List[str] = None) -> List[Dict[str, Any]]:
        """
        Augment dataset using various techniques / 使用各种技术增强数据集
        
        Args:
            data: Original dataset / 原始数据集
            methods: Augmentation methods to use / 要使用的增强方法
            
        Returns:
            Augmented dataset / 增强后的数据集
        """
        if methods is None:
            methods = ["synonym_replacement", "paraphrase", "question_variation"]
        
        augmented_data = data.copy()
        num_to_augment = int(len(data) * self.augmentation_ratio)
        
        logger.info(f"Augmenting {num_to_augment} samples using methods: {methods}")
        
        # Randomly select samples to augment / 随机选择要增强的样本
        samples_to_augment = random.sample(data, min(num_to_augment, len(data)))
        
        for sample in samples_to_augment:
            for method in methods:
                try:
                    if method == "synonym_replacement":
                        augmented_sample = self._synonym_replacement(sample)
                    elif method == "paraphrase":
                        augmented_sample = self._paraphrase_query(sample)
                    elif method == "question_variation":
                        augmented_sample = self._question_variation(sample)
                    else:
                        continue
                    
                    if augmented_sample:
                        augmented_data.append(augmented_sample)
                        
                except Exception as e:
                    logger.warning(f"Failed to augment sample with method {method}: {e}")
                    continue
        
        logger.info(f"Dataset augmented from {len(data)} to {len(augmented_data)} samples")
        return augmented_data
    
    def _synonym_replacement(self, sample: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Replace words with synonyms / 用同义词替换单词
        
        Args:
            sample: Original sample / 原始样本
            
        Returns:
            Augmented sample / 增强后的样本
        """
        query = sample.get("query", "")
        if not query:
            return None
        
        # Replace synonyms in query / 在查询中替换同义词
        augmented_query = query
        for original, synonyms in self.synonyms.items():
            if original in augmented_query and random.random() < 0.3:  # 30% chance to replace
                synonym = random.choice(synonyms)
                augmented_query = augmented_query.replace(original, synonym, 1)
        
        # Only return if query was actually changed / 只有在查询实际改变时才返回
        if augmented_query != query:
            augmented_sample = sample.copy()
            augmented_sample["query"] = augmented_query
            
            # Update messages if present / 如果存在则更新消息
            if "messages" in augmented_sample:
                messages = augmented_sample["messages"].copy()
                for msg in messages:
                    if msg["role"] == "user":
                        msg["content"] = augmented_query
                augmented_sample["messages"] = messages
            
            # Mark as augmented / 标记为增强数据
            if "metadata" not in augmented_sample:
                augmented_sample["metadata"] = {}
            augmented_sample["metadata"]["augmented"] = True
            augmented_sample["metadata"]["augmentation_method"] = "synonym_replacement"
            
            return augmented_sample
        
        return None
    
    def _paraphrase_query(self, sample: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Paraphrase the query / 改写查询
        
        Args:
            sample: Original sample / 原始样本
            
        Returns:
            Augmented sample / 增强后的样本
        """
        query = sample.get("query", "")
        if not query:
            return None
        
        # Simple paraphrasing patterns / 简单改写模式
        paraphrase_patterns = [
            (r"我(.*?)怎么办", r"请问\1应该如何处理"),
            (r"(.*?)是什么原因", r"\1的原因是什么"),
            (r"(.*?)需要注意什么", r"关于\1有什么注意事项"),
            (r"我(.*?)了", r"我出现了\1的情况"),
            (r"(.*?)怎么治疗", r"\1的治疗方法是什么"),
        ]
        
        augmented_query = query
        for pattern, replacement in paraphrase_patterns:
            if re.search(pattern, query):
                augmented_query = re.sub(pattern, replacement, query)
                break
        
        # Only return if query was actually changed / 只有在查询实际改变时才返回
        if augmented_query != query:
            augmented_sample = sample.copy()
            augmented_sample["query"] = augmented_query
            
            # Update messages if present / 如果存在则更新消息
            if "messages" in augmented_sample:
                messages = augmented_sample["messages"].copy()
                for msg in messages:
                    if msg["role"] == "user":
                        msg["content"] = augmented_query
                augmented_sample["messages"] = messages
            
            # Mark as augmented / 标记为增强数据
            if "metadata" not in augmented_sample:
                augmented_sample["metadata"] = {}
            augmented_sample["metadata"]["augmented"] = True
            augmented_sample["metadata"]["augmentation_method"] = "paraphrase"
            
            return augmented_sample
        
        return None
    
    def _question_variation(self, sample: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Create question variations / 创建问题变体
        
        Args:
            sample: Original sample / 原始样本
            
        Returns:
            Augmented sample / 增强后的样本
        """
        query = sample.get("query", "")
        if not query:
            return None
        
        # Question variation templates / 问题变体模板
        variations = [
            "请问{}",
            "关于{}，我想了解一下",
            "{}的情况下应该怎么办",
            "我想咨询一下{}",
            "{}这种情况正常吗",
        ]
        
        # Extract main content from query / 从查询中提取主要内容
        main_content = self._extract_main_content(query)
        if not main_content:
            return None
        
        # Create variation / 创建变体
        variation_template = random.choice(variations)
        augmented_query = variation_template.format(main_content)
        
        # Only return if query was actually changed / 只有在查询实际改变时才返回
        if augmented_query != query and len(augmented_query) > 5:
            augmented_sample = sample.copy()
            augmented_sample["query"] = augmented_query
            
            # Update messages if present / 如果存在则更新消息
            if "messages" in augmented_sample:
                messages = augmented_sample["messages"].copy()
                for msg in messages:
                    if msg["role"] == "user":
                        msg["content"] = augmented_query
                augmented_sample["messages"] = messages
            
            # Mark as augmented / 标记为增强数据
            if "metadata" not in augmented_sample:
                augmented_sample["metadata"] = {}
            augmented_sample["metadata"]["augmented"] = True
            augmented_sample["metadata"]["augmentation_method"] = "question_variation"
            
            return augmented_sample
        
        return None
    
    def _extract_main_content(self, query: str) -> str:
        """
        Extract main content from query / 从查询中提取主要内容
        
        Args:
            query: Input query / 输入查询
            
        Returns:
            Main content / 主要内容
        """
        # Remove common question words / 移除常见疑问词
        content = query
        
        # Remove question patterns / 移除问题模式
        patterns_to_remove = [
            r"请问",
            r"我想问",
            r"我想了解",
            r"怎么办",
            r"什么原因",
            r"需要注意什么",
            r"是否",
            r"会不会",
            r"能不能",
        ]
        
        for pattern in patterns_to_remove:
            content = re.sub(pattern, "", content)
        
        # Clean up / 清理
        content = content.strip("？?。，,！!")
        content = content.strip()
        
        return content if len(content) > 2 else query
    
    def balance_dataset(self, data: List[Dict[str, Any]], 
                       balance_key: str = "question_type") -> List[Dict[str, Any]]:
        """
        Balance dataset by augmenting underrepresented classes / 通过增强代表性不足的类别来平衡数据集
        
        Args:
            data: Original dataset / 原始数据集
            balance_key: Key to balance on / 平衡的键
            
        Returns:
            Balanced dataset / 平衡后的数据集
        """
        # Count samples by category / 按类别计算样本
        category_counts = {}
        for sample in data:
            category = sample.get("metadata", {}).get(balance_key, "unknown")
            category_counts[category] = category_counts.get(category, 0) + 1
        
        # Find target count (max count) / 找到目标计数（最大计数）
        target_count = max(category_counts.values())
        
        logger.info(f"Balancing dataset by {balance_key}. Target count: {target_count}")
        logger.info(f"Current distribution: {category_counts}")
        
        balanced_data = data.copy()
        
        # Augment underrepresented categories / 增强代表性不足的类别
        for category, count in category_counts.items():
            if count < target_count:
                samples_needed = target_count - count
                category_samples = [s for s in data if s.get("metadata", {}).get(balance_key) == category]
                
                # Augment samples for this category / 为此类别增强样本
                for _ in range(samples_needed):
                    if category_samples:
                        sample_to_augment = random.choice(category_samples)
                        augmented_sample = self._synonym_replacement(sample_to_augment)
                        if augmented_sample:
                            balanced_data.append(augmented_sample)
        
        logger.info(f"Dataset balanced from {len(data)} to {len(balanced_data)} samples")
        return balanced_data
