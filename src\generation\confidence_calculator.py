# -*- coding: utf-8 -*-
"""
Confidence Calculator / 置信度计算器

This module calculates confidence scores for generated medical responses.
该模块计算生成的医疗回复的置信度分数。
"""

import re
import numpy as np
from typing import Dict, List, Any, Optional
from sentence_transformers import SentenceTransformer
from dataclasses import dataclass

from ..utils import get_logger

logger = get_logger(__name__)


@dataclass
class ConfidenceResult:
    """Confidence calculation result / 置信度计算结果"""
    overall_confidence: float
    content_confidence: float
    context_relevance: float
    medical_certainty: float
    response_completeness: float
    factors: Dict[str, float]


class ConfidenceCalculator:
    """Confidence calculator for medical responses / 医疗回复置信度计算器"""
    
    def __init__(self, embedding_model: str = "BAAI/bge-large-zh-v1.5"):
        """
        Initialize confidence calculator / 初始化置信度计算器
        
        Args:
            embedding_model: Embedding model for similarity calculation / 用于相似度计算的嵌入模型
        """
        self.embedding_model = SentenceTransformer(embedding_model)
        
        # Confidence indicators / 置信度指标
        self.high_confidence_indicators = [
            "建议", "推荐", "可以", "通常", "一般", "常见", "可能",
            "医生", "医院", "检查", "治疗", "专业", "咨询"
        ]
        
        self.low_confidence_indicators = [
            "不确定", "可能", "也许", "大概", "估计", "猜测",
            "不清楚", "难以判断", "需要进一步"
        ]
        
        self.uncertainty_phrases = [
            "仅供参考", "建议咨询", "请就医", "专业诊断",
            "因人而异", "具体情况", "个体差异"
        ]
        
        logger.info("Initialized ConfidenceCalculator")
    
    def calculate_confidence(
        self, 
        query: str, 
        response: str, 
        context: List[str] = None
    ) -> ConfidenceResult:
        """
        Calculate confidence score for medical response / 计算医疗回复的置信度分数
        
        Args:
            query: User query / 用户查询
            response: Generated response / 生成的回复
            context: Retrieved context / 检索到的上下文
            
        Returns:
            Confidence calculation result / 置信度计算结果
        """
        logger.debug(f"Calculating confidence for response: {response[:50]}...")
        
        # Calculate individual confidence factors / 计算各个置信度因子
        content_confidence = self._calculate_content_confidence(response)
        context_relevance = self._calculate_context_relevance(query, response, context)
        medical_certainty = self._calculate_medical_certainty(response)
        response_completeness = self._calculate_response_completeness(query, response)
        
        # Calculate overall confidence / 计算总体置信度
        factors = {
            "content_confidence": content_confidence,
            "context_relevance": context_relevance,
            "medical_certainty": medical_certainty,
            "response_completeness": response_completeness
        }
        
        # Weighted average / 加权平均
        weights = {
            "content_confidence": 0.3,
            "context_relevance": 0.25,
            "medical_certainty": 0.25,
            "response_completeness": 0.2
        }
        
        overall_confidence = sum(
            factors[factor] * weights[factor] 
            for factor in factors
        )
        
        return ConfidenceResult(
            overall_confidence=overall_confidence,
            content_confidence=content_confidence,
            context_relevance=context_relevance,
            medical_certainty=medical_certainty,
            response_completeness=response_completeness,
            factors=factors
        )
    
    def _calculate_content_confidence(self, response: str) -> float:
        """Calculate content-based confidence / 计算基于内容的置信度"""
        score = 0.5  # Base score
        
        # Check for high confidence indicators / 检查高置信度指标
        high_count = sum(1 for indicator in self.high_confidence_indicators if indicator in response)
        score += min(0.3, high_count * 0.05)
        
        # Check for low confidence indicators / 检查低置信度指标
        low_count = sum(1 for indicator in self.low_confidence_indicators if indicator in response)
        score -= min(0.2, low_count * 0.05)
        
        # Check for uncertainty phrases (positive for medical responses) / 检查不确定性短语（对医疗回复是积极的）
        uncertainty_count = sum(1 for phrase in self.uncertainty_phrases if phrase in response)
        score += min(0.2, uncertainty_count * 0.1)
        
        # Response length factor / 回复长度因子
        length_score = min(1.0, len(response) / 200)  # Normalize to 200 chars
        score += length_score * 0.1
        
        return max(0.0, min(1.0, score))
    
    def _calculate_context_relevance(self, query: str, response: str, context: List[str] = None) -> float:
        """Calculate context relevance / 计算上下文相关性"""
        if not context:
            return 0.5  # Neutral score when no context
        
        try:
            # Calculate semantic similarity between query and response / 计算查询和回复的语义相似度
            query_embedding = self.embedding_model.encode([query])
            response_embedding = self.embedding_model.encode([response])
            
            query_response_sim = np.dot(query_embedding[0], response_embedding[0]) / (
                np.linalg.norm(query_embedding[0]) * np.linalg.norm(response_embedding[0])
            )
            
            # Calculate similarity between response and context / 计算回复和上下文的相似度
            context_embeddings = self.embedding_model.encode(context)
            context_similarities = []
            
            for ctx_embedding in context_embeddings:
                sim = np.dot(response_embedding[0], ctx_embedding) / (
                    np.linalg.norm(response_embedding[0]) * np.linalg.norm(ctx_embedding)
                )
                context_similarities.append(sim)
            
            # Use maximum similarity with context / 使用与上下文的最大相似度
            max_context_sim = max(context_similarities) if context_similarities else 0.0
            
            # Combine query-response and response-context similarities / 结合查询-回复和回复-上下文相似度
            relevance_score = (query_response_sim * 0.4 + max_context_sim * 0.6)
            
            return max(0.0, min(1.0, relevance_score))
            
        except Exception as e:
            logger.warning(f"Error calculating context relevance: {e}")
            return 0.5
    
    def _calculate_medical_certainty(self, response: str) -> float:
        """Calculate medical certainty / 计算医疗确定性"""
        score = 0.5  # Base score
        
        # Medical terminology usage / 医学术语使用
        medical_terms = [
            "症状", "诊断", "治疗", "药物", "疾病", "病因", "检查",
            "化验", "影像", "手术", "康复", "预防", "并发症"
        ]
        
        medical_term_count = sum(1 for term in medical_terms if term in response)
        score += min(0.3, medical_term_count * 0.05)
        
        # Professional language / 专业语言
        professional_phrases = [
            "根据症状", "建议检查", "可能需要", "请咨询医生",
            "专业诊断", "临床表现", "治疗方案"
        ]
        
        professional_count = sum(1 for phrase in professional_phrases if phrase in response)
        score += min(0.2, professional_count * 0.1)
        
        # Avoid overconfident statements / 避免过度自信的陈述
        overconfident_patterns = [
            r"一定是|肯定是|绝对是",
            r"100%|百分之百",
            r"保证|确保"
        ]
        
        for pattern in overconfident_patterns:
            if re.search(pattern, response, re.IGNORECASE):
                score -= 0.2
        
        return max(0.0, min(1.0, score))
    
    def _calculate_response_completeness(self, query: str, response: str) -> float:
        """Calculate response completeness / 计算回复完整性"""
        score = 0.0
        
        # Check if response addresses the query / 检查回复是否回应了查询
        query_keywords = self._extract_keywords(query)
        response_lower = response.lower()
        
        addressed_keywords = sum(1 for keyword in query_keywords if keyword in response_lower)
        if query_keywords:
            keyword_coverage = addressed_keywords / len(query_keywords)
            score += keyword_coverage * 0.4
        
        # Check for comprehensive medical response structure / 检查全面的医疗回复结构
        response_components = [
            "症状", "原因", "建议", "注意", "治疗", "预防", "检查"
        ]
        
        component_count = sum(1 for component in response_components if component in response)
        score += min(0.4, component_count * 0.1)
        
        # Response length appropriateness / 回复长度适当性
        if 50 <= len(response) <= 500:
            score += 0.2
        elif len(response) < 50:
            score -= 0.1
        
        return max(0.0, min(1.0, score))
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text / 从文本中提取关键词"""
        # Simple keyword extraction / 简单关键词提取
        # Remove common stop words / 移除常见停用词
        stop_words = {"的", "了", "是", "我", "你", "他", "她", "它", "们", "这", "那", "什么", "怎么", "为什么"}
        
        # Split and filter / 分割和过滤
        words = re.findall(r'[\u4e00-\u9fff]+', text)  # Chinese characters
        keywords = [word for word in words if len(word) > 1 and word not in stop_words]
        
        return keywords
    
    def get_confidence_explanation(self, confidence_result: ConfidenceResult) -> str:
        """
        Get explanation for confidence score / 获取置信度分数的解释
        
        Args:
            confidence_result: Confidence calculation result / 置信度计算结果
            
        Returns:
            Explanation text / 解释文本
        """
        overall = confidence_result.overall_confidence
        
        if overall >= 0.8:
            level = "高"
            explanation = "回复质量很好，内容专业且相关性强。"
        elif overall >= 0.6:
            level = "中等"
            explanation = "回复质量良好，但可能需要进一步完善。"
        elif overall >= 0.4:
            level = "较低"
            explanation = "回复质量一般，建议重新生成或补充信息。"
        else:
            level = "低"
            explanation = "回复质量较差，建议重新生成。"
        
        # Add factor details / 添加因子详情
        factors_text = []
        for factor, score in confidence_result.factors.items():
            factor_names = {
                "content_confidence": "内容置信度",
                "context_relevance": "上下文相关性",
                "medical_certainty": "医疗确定性",
                "response_completeness": "回复完整性"
            }
            factors_text.append(f"{factor_names[factor]}: {score:.2f}")
        
        return f"置信度等级: {level} ({overall:.2f})\n{explanation}\n详细评分: {', '.join(factors_text)}"


def main():
    """Test confidence calculator / 测试置信度计算器"""
    try:
        calculator = ConfidenceCalculator()
        
        # Test cases / 测试用例
        test_cases = [
            {
                "query": "我头痛怎么办？",
                "response": "头痛可能有多种原因，包括紧张、疲劳、感冒等。建议您先注意休息，保持充足睡眠，如果症状持续或加重，请及时咨询专业医生进行检查。以上建议仅供参考。",
                "context": ["头痛的常见原因包括紧张性头痛、偏头痛等", "充足的休息有助于缓解头痛症状"]
            },
            {
                "query": "感冒了吃什么药？",
                "response": "感冒了可以吃点感冒药，多喝水就行。",
                "context": []
            }
        ]
        
        for i, case in enumerate(test_cases):
            print(f"\n测试用例 {i+1}:")
            print(f"查询: {case['query']}")
            print(f"回复: {case['response']}")
            
            result = calculator.calculate_confidence(
                case['query'], 
                case['response'], 
                case['context']
            )
            
            print(f"总体置信度: {result.overall_confidence:.2f}")
            print(f"各项评分:")
            print(f"  内容置信度: {result.content_confidence:.2f}")
            print(f"  上下文相关性: {result.context_relevance:.2f}")
            print(f"  医疗确定性: {result.medical_certainty:.2f}")
            print(f"  回复完整性: {result.response_completeness:.2f}")
            
            explanation = calculator.get_confidence_explanation(result)
            print(f"解释: {explanation}")
        
        logger.info("Confidence calculator test completed successfully!")
        
    except Exception as e:
        logger.error(f"Error in confidence calculator test: {e}")
        raise


if __name__ == "__main__":
    main()
