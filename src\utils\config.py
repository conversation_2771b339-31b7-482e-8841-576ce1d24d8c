# -*- coding: utf-8 -*-
"""
Configuration Management Module / 配置管理模块

This module handles loading and managing configuration files for the GRAG system.
该模块处理GRAG系统的配置文件加载和管理。
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables / 加载环境变量
load_dotenv()


@dataclass
class ModelConfig:
    """Model configuration / 模型配置"""
    base_model: str
    model_type: str
    max_length: int
    temperature: float
    top_p: float
    top_k: int
    load_in_4bit: bool
    lora_config: Dict[str, Any]


@dataclass
class TrainingConfig:
    """Training configuration / 训练配置"""
    sft: Dict[str, Any]
    grpo: Dict[str, Any]


@dataclass
class DataConfig:
    """Data configuration / 数据配置"""
    dataset_name: str
    train_split: float
    val_split: float
    test_split: float
    max_samples: int
    preprocessing: Dict[str, Any]


@dataclass
class RetrievalConfig:
    """Retrieval configuration / 检索配置"""
    vector_db: Dict[str, Any]
    embedding: Dict[str, Any]
    search: Dict[str, Any]
    reranking: Dict[str, Any]
    self_correction: Dict[str, Any]


@dataclass
class WebSearchConfig:
    """Web search configuration / 网络搜索配置"""
    enabled: bool
    provider: str
    api_key: str
    max_results: int
    search_depth: str
    time_range: str
    domains_whitelist: list
    fallback_enabled: bool


@dataclass
class FrontendConfig:
    """Frontend configuration / 前端配置"""
    title: str
    subtitle: str
    description: str
    max_history: int
    show_sources: bool
    show_confidence: bool
    show_retrieval_details: bool
    theme: str
    language: str


@dataclass
class LoggingConfig:
    """Logging configuration / 日志配置"""
    level: str
    file: str
    format: str
    rotation: str
    retention: str


@dataclass
class EnvironmentConfig:
    """Environment configuration / 环境配置"""
    device: str
    mixed_precision: bool
    compile_model: bool
    seed: int
    deterministic: bool


class ConfigManager:
    """Configuration manager / 配置管理器"""
    
    def __init__(self, config_path: str = "configs/config.yaml"):
        """
        Initialize configuration manager / 初始化配置管理器
        
        Args:
            config_path: Path to configuration file / 配置文件路径
        """
        self.config_path = Path(config_path)
        self.config = self._load_config()
        self._validate_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """
        Load configuration from YAML file / 从YAML文件加载配置
        
        Returns:
            Configuration dictionary / 配置字典
        """
        if not self.config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
            
        with open(self.config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            
        # Replace environment variables / 替换环境变量
        config = self._replace_env_vars(config)
        return config
    
    def _replace_env_vars(self, config: Any) -> Any:
        """
        Replace environment variables in configuration / 替换配置中的环境变量
        
        Args:
            config: Configuration object / 配置对象
            
        Returns:
            Configuration with environment variables replaced / 替换环境变量后的配置
        """
        if isinstance(config, dict):
            return {k: self._replace_env_vars(v) for k, v in config.items()}
        elif isinstance(config, list):
            return [self._replace_env_vars(item) for item in config]
        elif isinstance(config, str) and config.startswith("${") and config.endswith("}"):
            env_var = config[2:-1]
            return os.getenv(env_var, config)
        else:
            return config
    
    def _validate_config(self):
        """Validate configuration / 验证配置"""
        required_sections = [
            "model", "training", "data", "retrieval", 
            "web_search", "frontend", "logging", "environment"
        ]
        
        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"Missing required configuration section: {section}")
    
    @property
    def model(self) -> ModelConfig:
        """Get model configuration / 获取模型配置"""
        return ModelConfig(**self.config["model"])
    
    @property
    def training(self) -> TrainingConfig:
        """Get training configuration / 获取训练配置"""
        return TrainingConfig(**self.config["training"])
    
    @property
    def data(self) -> DataConfig:
        """Get data configuration / 获取数据配置"""
        return DataConfig(**self.config["data"])
    
    @property
    def retrieval(self) -> RetrievalConfig:
        """Get retrieval configuration / 获取检索配置"""
        return RetrievalConfig(**self.config["retrieval"])
    
    @property
    def web_search(self) -> WebSearchConfig:
        """Get web search configuration / 获取网络搜索配置"""
        return WebSearchConfig(**self.config["web_search"])
    
    @property
    def frontend(self) -> FrontendConfig:
        """Get frontend configuration / 获取前端配置"""
        return FrontendConfig(**self.config["frontend"])
    
    @property
    def logging_config(self) -> LoggingConfig:
        """Get logging configuration / 获取日志配置"""
        return LoggingConfig(**self.config["logging"])
    
    @property
    def environment(self) -> EnvironmentConfig:
        """Get environment configuration / 获取环境配置"""
        return EnvironmentConfig(**self.config["environment"])
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value by key / 通过键获取配置值
        
        Args:
            key: Configuration key (supports dot notation) / 配置键（支持点记法）
            default: Default value if key not found / 键不存在时的默认值
            
        Returns:
            Configuration value / 配置值
        """
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
                
        return value
    
    def update(self, key: str, value: Any):
        """
        Update configuration value / 更新配置值
        
        Args:
            key: Configuration key (supports dot notation) / 配置键（支持点记法）
            value: New value / 新值
        """
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
            
        config[keys[-1]] = value
    
    def save(self, path: Optional[str] = None):
        """
        Save configuration to file / 保存配置到文件
        
        Args:
            path: Output path (default: original config path) / 输出路径（默认：原配置路径）
        """
        output_path = Path(path) if path else self.config_path
        
        with open(output_path, 'w', encoding='utf-8') as f:
            yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)


# Global configuration instance / 全局配置实例
# Note: Lazy initialization to avoid import-time errors
_config_manager = None

def get_config_manager() -> ConfigManager:
    """Get or create global configuration manager / 获取或创建全局配置管理器"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager

# For backward compatibility - will be initialized when first accessed
# 向后兼容 - 首次访问时初始化
config_manager = None
