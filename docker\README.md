# GRAG Docker 部署指南

本文件夹包含了GRAG项目的Docker配置文件，支持开发和生产环境的容器化部署。

## 📁 文件结构

```
docker/
├── Dockerfile                 # 主要的Docker镜像定义
├── docker-compose.yml         # 基础Docker Compose配置
├── docker-compose.dev.yml     # 开发环境覆盖配置
├── docker-compose.prod.yml    # 生产环境覆盖配置
├── .dockerignore              # Docker构建忽略文件
└── README.md                  # 本文档
```

## 🚀 快速开始

### 开发环境

1. **启动开发环境**：
   ```bash
   cd docker
   docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build
   ```

2. **访问服务**：
   - Streamlit应用: http://localhost:8501
   - Jupyter Lab: http://localhost:8888

### 生产环境

1. **启动生产环境**：
   ```bash
   cd docker
   docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build
   ```

2. **访问服务**：
   - 主应用: http://localhost:8501
   - 监控面板: http://localhost:3000 (Grafana)
   - 指标收集: http://localhost:9090 (Prometheus)

## 🔧 配置说明

### 环境变量

在项目根目录创建 `.env` 文件：

```bash
# API Keys
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
TAVILY_API_KEY=your_tavily_key

# Database
REDIS_PASSWORD=your_redis_password
POSTGRES_PASSWORD=your_postgres_password

# Monitoring
GRAFANA_PASSWORD=your_grafana_password

# CUDA (if using GPU)
CUDA_VISIBLE_DEVICES=0
```

### 服务配置

#### 主应用 (grag-app)
- **端口**: 8501
- **健康检查**: 自动检测应用状态
- **资源限制**: 生产环境限制内存和CPU使用

#### 向量数据库 (vector-db)
- **类型**: Qdrant
- **端口**: 6333 (HTTP), 6334 (gRPC)
- **存储**: 持久化卷存储

#### 缓存 (redis)
- **端口**: 6379
- **持久化**: AOF模式
- **密码保护**: 通过环境变量配置

#### 监控 (prometheus + grafana)
- **Prometheus**: 9090端口，收集指标
- **Grafana**: 3000端口，可视化面板

## 🛠️ 常用命令

### 构建和启动

```bash
# 开发环境
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build

# 生产环境
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build

# 仅启动主应用
docker-compose up grag-app

# 启动特定profile
docker-compose --profile monitoring up  # 启动监控服务
docker-compose --profile cache up       # 启动缓存服务
```

### 管理容器

```bash
# 查看运行状态
docker-compose ps

# 查看日志
docker-compose logs grag-app
docker-compose logs -f  # 实时日志

# 停止服务
docker-compose down

# 停止并删除卷
docker-compose down -v

# 重启服务
docker-compose restart grag-app
```

### 调试和维护

```bash
# 进入容器
docker-compose exec grag-app bash

# 查看容器资源使用
docker stats

# 清理未使用的镜像和容器
docker system prune -a
```

## 📊 监控和日志

### 健康检查
所有服务都配置了健康检查，可以通过以下方式查看：

```bash
docker-compose ps  # 查看健康状态
docker inspect <container_name> | grep Health  # 详细健康信息
```

### 日志管理
- 应用日志: 挂载到 `../logs` 目录
- Nginx日志: 存储在 `nginx_logs` 卷中
- 容器日志: 使用 `docker-compose logs` 查看

### 监控指标
- **Prometheus**: 收集应用和系统指标
- **Grafana**: 提供可视化面板
- **默认面板**: 包含CPU、内存、请求量等关键指标

## 🔒 安全配置

### 网络安全
- 所有服务运行在独立的Docker网络中
- 仅暴露必要的端口
- 生产环境建议使用Nginx反向代理

### 数据安全
- 敏感数据通过环境变量配置
- 数据库密码保护
- SSL证书支持（需要配置nginx/ssl目录）

### 容器安全
- 使用非root用户运行应用
- 最小化镜像体积
- 定期更新基础镜像

## 🚨 故障排除

### 常见问题

1. **端口冲突**：
   ```bash
   # 检查端口占用
   lsof -i :8501
   # 修改docker-compose.yml中的端口映射
   ```

2. **内存不足**：
   ```bash
   # 增加Docker内存限制
   # 或在docker-compose.yml中调整资源限制
   ```

3. **权限问题**：
   ```bash
   # 确保数据目录权限正确
   sudo chown -R 1000:1000 ../data ../models ../logs
   ```

4. **网络问题**：
   ```bash
   # 重建网络
   docker-compose down
   docker network prune
   docker-compose up
   ```

### 调试模式

启用调试模式：
```bash
# 设置环境变量
export LOG_LEVEL=DEBUG

# 或在docker-compose.yml中设置
environment:
  - LOG_LEVEL=DEBUG
```

## 📈 性能优化

### 资源配置
- 根据实际需求调整内存和CPU限制
- 使用SSD存储提高I/O性能
- 配置合适的并发数

### 缓存优化
- 启用Redis缓存服务
- 配置适当的缓存策略
- 监控缓存命中率

### 网络优化
- 使用CDN加速静态资源
- 配置Nginx压缩
- 启用HTTP/2

## 🔄 更新和维护

### 更新应用
```bash
# 拉取最新代码
git pull

# 重新构建并启动
docker-compose up --build -d
```

### 备份数据
```bash
# 备份向量数据库
docker-compose exec vector-db qdrant-backup

# 备份Redis数据
docker-compose exec redis redis-cli BGSAVE
```

### 清理旧数据
```bash
# 清理旧镜像
docker image prune -a

# 清理未使用的卷
docker volume prune
```

## 📞 支持

如果遇到问题，请：
1. 检查日志: `docker-compose logs`
2. 验证配置: `docker-compose config`
3. 查看资源使用: `docker stats`
4. 参考故障排除部分

更多信息请参考项目主README文档。
