# -*- coding: utf-8 -*-
"""
Group Relative Policy Optimization (GRPO) Trainer / 群体相对策略优化训练器

This module implements GRPO training for the medical RAG system using Unsloth.
该模块使用Unsloth实现医疗RAG系统的GRPO训练。
"""

import os
import re
import torch
import numpy as np
from typing import Dict, List, Any, Optional, Callable
from pathlib import Path
from datasets import Dataset
from vllm import SamplingParams
from trl import GRPOConfig, GRPOTrainer
from unsloth import FastLanguageModel
import wandb

from ..utils import get_logger, config_manager, set_seed
from ..data_processing import MedicalDatasetLoader
from .reward_models import (
    MedicalAccuracyReward,
    RelevanceReward, 
    SafetyReward,
    CompletenessReward,
    FormatComplianceReward
)

logger = get_logger(__name__)


class MedicalGRPOTrainer:
    """Medical GRPO trainer / 医疗GRPO训练器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize GRPO trainer / 初始化GRPO训练器
        
        Args:
            config: Training configuration / 训练配置
        """
        self.config = config or config_manager.training.grpo
        self.model_config = config_manager.model
        self.reward_config = config_manager.reward_models
        
        self.model = None
        self.tokenizer = None
        self.trainer = None
        self.reward_functions = []
        
        # Set random seed / 设置随机种子
        set_seed(config_manager.environment.seed)
        
        # Setup medical reasoning format / 设置医疗推理格式
        self.reasoning_start = "<医疗分析>"
        self.reasoning_end = "</医疗分析>"
        self.solution_start = "<建议方案>"
        self.solution_end = "</建议方案>"
        
        logger.info("Initialized Medical GRPO trainer")
    
    def load_model(self, sft_model_path: str = None):
        """
        Load SFT model for GRPO training / 加载SFT模型进行GRPO训练
        
        Args:
            sft_model_path: Path to SFT model / SFT模型路径
        """
        if sft_model_path and Path(sft_model_path).exists():
            model_path = sft_model_path
            logger.info(f"Loading SFT model from: {model_path}")
        else:
            model_path = self.model_config.base_model
            logger.info(f"Loading base model: {model_path}")
        
        # Load model with Unsloth / 使用Unsloth加载模型
        self.model, self.tokenizer = FastLanguageModel.from_pretrained(
            model_name=model_path,
            max_seq_length=self.model_config.max_length,
            load_in_4bit=False,  # Use 16bit for GRPO
            fast_inference=True,  # Enable vLLM fast inference
            max_lora_rank=self.model_config.lora_config["r"],
            gpu_memory_utilization=0.7,
        )
        
        # Apply LoRA for GRPO / 为GRPO应用LoRA
        self.model = FastLanguageModel.get_peft_model(
            self.model,
            r=self.model_config.lora_config["r"],
            target_modules=self.model_config.lora_config["target_modules"],
            lora_alpha=self.model_config.lora_config["lora_alpha"] * 2,  # *2 speeds up training
            use_gradient_checkpointing="unsloth",
            random_state=config_manager.environment.seed,
        )
        
        # Setup medical chat template / 设置医疗聊天模板
        self._setup_medical_chat_template()
        
        logger.info("Model loaded and configured for GRPO training")
    
    def _setup_medical_chat_template(self):
        """Setup medical reasoning chat template / 设置医疗推理聊天模板"""
        system_prompt = f"""你是一个专业的医疗助手。请仔细分析患者的问题，提供准确、安全的医疗建议。
请将你的分析过程放在{self.reasoning_start}和{self.reasoning_end}之间。
然后将你的建议方案放在{self.solution_start}和{self.solution_end}之间。"""
        
        chat_template = \
            "{% if messages[0]['role'] == 'system' %}"\
                "{{ messages[0]['content'] + eos_token }}"\
                "{% set loop_messages = messages[1:] %}"\
            "{% else %}"\
                f"{{ '{system_prompt}' + eos_token }}"\
                "{% set loop_messages = messages %}"\
            "{% endif %}"\
            "{% for message in loop_messages %}"\
                "{% if message['role'] == 'user' %}"\
                    "{{ message['content'] }}"\
                "{% elif message['role'] == 'assistant' %}"\
                    "{{ message['content'] + eos_token }}"\
                "{% endif %}"\
            "{% endfor %}"\
            f"{% if add_generation_prompt %}{{ '{self.reasoning_start}' }}"\
            "{% endif %}"
        
        # Replace with specific template / 替换为特定模板
        chat_template = chat_template\
            .replace("'{system_prompt}'", f"'{system_prompt}'")\
            .replace(f"'{self.reasoning_start}'", f"'{self.reasoning_start}'")
        
        self.tokenizer.chat_template = chat_template
        logger.info("Medical chat template configured")
    
    def setup_reward_functions(self):
        """Setup reward functions for GRPO / 设置GRPO的奖励函数"""
        logger.info("Setting up reward functions...")
        
        self.reward_functions = []
        
        # Medical accuracy reward / 医疗准确性奖励
        if "medical_accuracy" in self.config["reward_functions"]:
            medical_reward = MedicalAccuracyReward(self.reward_config["medical_accuracy"])
            self.reward_functions.append(medical_reward.calculate_reward)
        
        # Relevance reward / 相关性奖励
        if "relevance" in self.config["reward_functions"]:
            relevance_reward = RelevanceReward(self.reward_config["relevance"])
            self.reward_functions.append(relevance_reward.calculate_reward)
        
        # Safety reward / 安全性奖励
        if "safety" in self.config["reward_functions"]:
            safety_reward = SafetyReward(self.reward_config["safety"])
            self.reward_functions.append(safety_reward.calculate_reward)
        
        # Completeness reward / 完整性奖励
        if "completeness" in self.config["reward_functions"]:
            completeness_reward = CompletenessReward(self.reward_config["completeness"])
            self.reward_functions.append(completeness_reward.calculate_reward)
        
        # Format compliance reward / 格式合规性奖励
        if "format_compliance" in self.config["reward_functions"]:
            format_reward = FormatComplianceReward(self.reward_config["format_compliance"])
            self.reward_functions.append(format_reward.calculate_reward)
        
        # Add format matching rewards / 添加格式匹配奖励
        self.reward_functions.extend([
            self._match_format_exactly,
            self._match_format_approximately,
            self._check_medical_answer
        ])
        
        logger.info(f"Setup {len(self.reward_functions)} reward functions")
    
    def _match_format_exactly(self, completions, **kwargs):
        """Reward exact format matching / 奖励精确格式匹配"""
        scores = []
        
        # Create regex pattern / 创建正则表达式模式
        solution_end_regex = rf"{re.escape(self.solution_end)}[\s]*" + \
            "(?:" + re.escape(self.tokenizer.eos_token) + ")?"
        
        match_format = re.compile(
            rf"{re.escape(self.reasoning_end)}.*?"\
            rf"{re.escape(self.solution_start)}(.+?){solution_end_regex}"\
            rf"[\s]*$",
            flags=re.MULTILINE | re.DOTALL
        )
        
        for completion in completions:
            score = 0
            response = completion[0]["content"]
            # Match if format is seen exactly / 如果格式完全匹配则奖励
            if match_format.search(response) is not None:
                score += 3.0
            scores.append(score)
        
        return scores
    
    def _match_format_approximately(self, completions, **kwargs):
        """Reward approximate format matching / 奖励近似格式匹配"""
        scores = []
        
        for completion in completions:
            score = 0
            response = completion[0]["content"]
            
            # Count format elements / 计算格式元素
            score += 0.5 if response.count(self.reasoning_end) == 1 else -1.0
            score += 0.5 if response.count(self.solution_start) == 1 else -1.0
            score += 0.5 if response.count(self.solution_end) == 1 else -1.0
            
            scores.append(score)
        
        return scores
    
    def _check_medical_answer(self, prompts, completions, answer, **kwargs):
        """Check medical answer quality / 检查医疗答案质量"""
        scores = []
        
        # Extract solution from format / 从格式中提取解决方案
        solution_end_regex = rf"{re.escape(self.solution_end)}[\s]*" + \
            "(?:" + re.escape(self.tokenizer.eos_token) + ")?"
        
        match_format = re.compile(
            rf"{re.escape(self.reasoning_end)}.*?"\
            rf"{re.escape(self.solution_start)}(.+?){solution_end_regex}"\
            rf"[\s]*$",
            flags=re.MULTILINE | re.DOTALL
        )
        
        for completion, true_answer in zip(completions, answer):
            score = 0
            response = completion[0]["content"]
            
            # Extract generated solution / 提取生成的解决方案
            match = match_format.search(response)
            if match is None:
                scores.append(-2.0)
                continue
            
            generated_solution = match.group(1).strip()
            
            # Simple similarity check / 简单相似性检查
            if true_answer.lower() in generated_solution.lower():
                score += 2.0
            elif any(word in generated_solution.lower() for word in true_answer.lower().split()):
                score += 1.0
            else:
                score -= 1.0
            
            scores.append(score)
        
        return scores
    
    def prepare_dataset(self, data_path: str = None) -> Dataset:
        """
        Prepare GRPO training dataset / 准备GRPO训练数据集
        
        Args:
            data_path: Path to training data / 训练数据路径
            
        Returns:
            Prepared dataset / 准备好的数据集
        """
        logger.info("Preparing GRPO training dataset...")
        
        if data_path and Path(data_path).exists():
            # Load from file / 从文件加载
            loader = MedicalDatasetLoader()
            train_data = loader.load_processed_data("train", Path(data_path).parent)
        else:
            # Load and process from scratch / 从头加载和处理
            loader = MedicalDatasetLoader()
            raw_dataset = loader.load_raw_dataset()
            processed_data = loader.preprocess_data(raw_dataset)
            train_data, _, _ = loader.split_dataset(processed_data)
            train_data = loader.prepare_grpo_data(train_data)
        
        # Filter by prompt length / 按提示长度过滤
        def length_filter(example):
            # Use more efficient tokenization with __call__ method
            prompt_text = self.tokenizer.apply_chat_template(
                example["prompt"],
                add_generation_prompt=True,
                tokenize=False
            )
            # Use __call__ method for efficient tokenization
            prompt_tokens = self.tokenizer(
                prompt_text,
                return_tensors=None,  # Don't return tensors, just token IDs
                padding=False,
                truncation=False
            )["input_ids"]
            return len(prompt_tokens) <= self.config["max_prompt_length"]
        
        dataset = Dataset.from_list(train_data)
        dataset = dataset.filter(length_filter)
        
        logger.info(f"Prepared GRPO dataset with {len(dataset)} samples")
        return dataset

    def train(self, train_dataset: Dataset, eval_dataset: Optional[Dataset] = None):
        """
        Start GRPO training / 开始GRPO训练

        Args:
            train_dataset: Training dataset / 训练数据集
            eval_dataset: Evaluation dataset / 评估数据集
        """
        logger.info("Starting GRPO training...")

        if self.model is None:
            raise ValueError("Model not loaded. Call load_model() first.")

        if not self.reward_functions:
            self.setup_reward_functions()

        # Setup vLLM sampling parameters / 设置vLLM采样参数
        vllm_sampling_params = SamplingParams(
            min_p=0.1,
            top_p=1.0,
            top_k=-1,
            seed=config_manager.environment.seed,
            stop=[self.tokenizer.eos_token],
            include_stop_str_in_output=True,
        )

        # Setup training arguments / 设置训练参数
        training_args = GRPOConfig(
            vllm_sampling_params=vllm_sampling_params,
            temperature=self.config["temperature"],
            learning_rate=self.config["learning_rate"],
            weight_decay=0.01,
            warmup_ratio=0.1,
            lr_scheduler_type="linear",
            optim="adamw_8bit",
            logging_steps=1,
            per_device_train_batch_size=self.config["per_device_train_batch_size"],
            gradient_accumulation_steps=1,
            num_generations=self.config["num_generations"],
            max_prompt_length=self.config["max_prompt_length"],
            max_completion_length=self.config["max_completion_length"],
            num_train_epochs=self.config["num_train_epochs"],
            save_steps=500,
            output_dir=self.config["output_dir"],
            report_to="wandb" if wandb.run else "none",
            evaluation_strategy="steps" if eval_dataset else "no",
            eval_steps=100 if eval_dataset else None,
        )

        # Initialize GRPO trainer / 初始化GRPO训练器
        self.trainer = GRPOTrainer(
            model=self.model,
            processing_class=self.tokenizer,
            reward_funcs=self.reward_functions,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
        )

        # Start training / 开始训练
        self.trainer.train()

        logger.info("GRPO training completed")

    def save_model(self, output_dir: str = None):
        """
        Save trained GRPO model / 保存训练好的GRPO模型

        Args:
            output_dir: Output directory / 输出目录
        """
        output_dir = output_dir or self.config["output_dir"]

        logger.info(f"Saving GRPO model to {output_dir}")

        # Save LoRA adapter / 保存LoRA适配器
        self.model.save_lora(f"{output_dir}/grpo_lora")

        # Save full model / 保存完整模型
        self.model.save_pretrained(output_dir)
        self.tokenizer.save_pretrained(output_dir)

        # Save merged model for inference / 保存合并模型用于推理
        merged_dir = Path(output_dir) / "merged"
        self.model.save_pretrained_merged(
            str(merged_dir),
            self.tokenizer,
            save_method="merged_16bit"
        )

        logger.info("GRPO model saved successfully")

    def generate_response(self, query: str, max_tokens: int = 1024) -> str:
        """
        Generate response using trained model / 使用训练好的模型生成回复

        Args:
            query: Input query / 输入查询
            max_tokens: Maximum tokens to generate / 最大生成token数

        Returns:
            Generated response / 生成的回复
        """
        if self.model is None:
            raise ValueError("Model not loaded")

        # Format input / 格式化输入
        messages = [
            {"role": "system", "content": "你是一个专业的医疗助手，能够提供准确、安全的医疗建议。"},
            {"role": "user", "content": query}
        ]

        text = self.tokenizer.apply_chat_template(
            messages,
            tokenize=False,
            add_generation_prompt=True
        )

        # Generate response / 生成回复
        sampling_params = SamplingParams(
            temperature=0.7,
            top_k=50,
            max_tokens=max_tokens,
        )

        output = self.model.fast_generate(
            [text],
            sampling_params=sampling_params,
            lora_request=None,
        )[0].outputs[0].text

        return output


def main():
    """Test GRPO trainer / 测试GRPO训练器"""
    try:
        # Initialize trainer / 初始化训练器
        trainer = MedicalGRPOTrainer()

        # Load model (preferably SFT model) / 加载模型（最好是SFT模型）
        sft_model_path = config_manager.training.sft["output_dir"]
        trainer.load_model(sft_model_path)

        # Setup reward functions / 设置奖励函数
        trainer.setup_reward_functions()

        # Prepare dataset / 准备数据集
        train_dataset = trainer.prepare_dataset()

        # Start training / 开始训练
        trainer.train(train_dataset)

        # Save model / 保存模型
        trainer.save_model()

        logger.info("GRPO training completed successfully!")

    except Exception as e:
        logger.error(f"Error in GRPO training: {e}")
        raise


if __name__ == "__main__":
    main()
