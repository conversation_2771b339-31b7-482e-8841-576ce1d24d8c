#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Application Runner / 应用启动器

This script provides a unified way to run different components of the GRAG system.
该脚本提供了运行GRAG系统不同组件的统一方式。
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

# Add src to path / 添加src到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from utils import get_logger, config_manager

logger = get_logger(__name__)


def run_frontend(port: int = 8501, host: str = "localhost"):
    """
    Run Streamlit frontend / 运行Streamlit前端
    
    Args:
        port: Port number / 端口号
        host: Host address / 主机地址
    """
    logger.info(f"Starting Streamlit frontend on {host}:{port}")
    
    frontend_path = Path(__file__).parent.parent / "src" / "frontend" / "app.py"
    
    cmd = [
        "streamlit", "run", str(frontend_path),
        "--server.port", str(port),
        "--server.address", host,
        "--server.headless", "true",
        "--browser.gatherUsageStats", "false"
    ]
    
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to start frontend: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        logger.info("Frontend stopped by user")


def run_data_download():
    """Run data download script / 运行数据下载脚本"""
    logger.info("Starting data download and processing...")
    
    script_path = Path(__file__).parent / "download_data.py"
    
    try:
        subprocess.run([sys.executable, str(script_path)], check=True)
        logger.info("Data download completed successfully")
    except subprocess.CalledProcessError as e:
        logger.error(f"Data download failed: {e}")
        sys.exit(1)


def run_sft_training(use_wandb: bool = False):
    """
    Run SFT training / 运行SFT训练
    
    Args:
        use_wandb: Whether to use Weights & Biases / 是否使用Weights & Biases
    """
    logger.info("Starting SFT training...")
    
    script_path = Path(__file__).parent / "train_sft.py"
    cmd = [sys.executable, str(script_path)]
    
    if use_wandb:
        cmd.append("--wandb")
    
    try:
        subprocess.run(cmd, check=True)
        logger.info("SFT training completed successfully")
    except subprocess.CalledProcessError as e:
        logger.error(f"SFT training failed: {e}")
        sys.exit(1)


def run_grpo_training(use_wandb: bool = False):
    """
    Run GRPO training / 运行GRPO训练
    
    Args:
        use_wandb: Whether to use Weights & Biases / 是否使用Weights & Biases
    """
    logger.info("Starting GRPO training...")
    
    script_path = Path(__file__).parent / "train_grpo.py"
    cmd = [sys.executable, str(script_path)]
    
    if use_wandb:
        cmd.append("--wandb")
    
    try:
        subprocess.run(cmd, check=True)
        logger.info("GRPO training completed successfully")
    except subprocess.CalledProcessError as e:
        logger.error(f"GRPO training failed: {e}")
        sys.exit(1)


def run_model_test():
    """Run model testing / 运行模型测试"""
    logger.info("Starting model testing...")
    
    script_path = Path(__file__).parent / "test_model.py"
    
    try:
        subprocess.run([sys.executable, str(script_path)], check=True)
        logger.info("Model testing completed successfully")
    except subprocess.CalledProcessError as e:
        logger.error(f"Model testing failed: {e}")
        sys.exit(1)


def run_setup_test():
    """Run setup test / 运行设置测试"""
    logger.info("Running setup test...")
    
    script_path = Path(__file__).parent / "test_setup.py"
    
    try:
        subprocess.run([sys.executable, str(script_path)], check=True)
        logger.info("Setup test completed successfully")
    except subprocess.CalledProcessError as e:
        logger.error(f"Setup test failed: {e}")
        sys.exit(1)


def run_full_pipeline(use_wandb: bool = False):
    """
    Run full training pipeline / 运行完整训练流水线
    
    Args:
        use_wandb: Whether to use Weights & Biases / 是否使用Weights & Biases
    """
    logger.info("Starting full training pipeline...")
    
    # Step 1: Download data / 步骤1：下载数据
    logger.info("Step 1: Downloading and processing data...")
    run_data_download()
    
    # Step 2: SFT training / 步骤2：SFT训练
    logger.info("Step 2: Starting SFT training...")
    run_sft_training(use_wandb)
    
    # Step 3: GRPO training / 步骤3：GRPO训练
    logger.info("Step 3: Starting GRPO training...")
    run_grpo_training(use_wandb)
    
    # Step 4: Model testing / 步骤4：模型测试
    logger.info("Step 4: Testing trained model...")
    run_model_test()
    
    logger.info("Full pipeline completed successfully!")
    logger.info("You can now start the frontend with: python scripts/run_app.py frontend")


def print_status():
    """Print system status / 打印系统状态"""
    print("\n" + "="*60)
    print("GRAG SYSTEM STATUS / GRAG系统状态")
    print("="*60)
    
    # Check data / 检查数据
    data_dir = Path("data/processed")
    if data_dir.exists() and (data_dir / "train.jsonl").exists():
        print("✅ Data: Ready")
    else:
        print("❌ Data: Not ready (run: python scripts/run_app.py data)")
    
    # Check SFT model / 检查SFT模型
    sft_dir = Path(config_manager.training.sft["output_dir"])
    if sft_dir.exists() and (sft_dir / "pytorch_model.bin").exists():
        print("✅ SFT Model: Ready")
    else:
        print("❌ SFT Model: Not ready (run: python scripts/run_app.py sft)")
    
    # Check GRPO model / 检查GRPO模型
    grpo_dir = Path(config_manager.training.grpo["output_dir"])
    if grpo_dir.exists() and (grpo_dir / "pytorch_model.bin").exists():
        print("✅ GRPO Model: Ready")
    else:
        print("❌ GRPO Model: Not ready (run: python scripts/run_app.py grpo)")
    
    # Check vector store / 检查向量存储
    vector_path = Path(config_manager.retrieval.vector_db["save_path"])
    if vector_path.exists():
        print("✅ Vector Store: Ready")
    else:
        print("❌ Vector Store: Not ready (run: python scripts/run_app.py data)")
    
    print("="*60)
    print("Available commands:")
    print("  python scripts/run_app.py status     - Show this status")
    print("  python scripts/run_app.py data       - Download and process data")
    print("  python scripts/run_app.py sft        - Train SFT model")
    print("  python scripts/run_app.py grpo       - Train GRPO model")
    print("  python scripts/run_app.py test       - Test trained model")
    print("  python scripts/run_app.py frontend   - Start web interface")
    print("  python scripts/run_app.py pipeline   - Run full training pipeline")
    print("="*60)


def main():
    """Main function / 主函数"""
    parser = argparse.ArgumentParser(description="GRAG System Application Runner")
    
    parser.add_argument(
        "command",
        choices=["frontend", "data", "sft", "grpo", "test", "setup", "pipeline", "status"],
        help="Command to run"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=8501,
        help="Port for frontend (default: 8501)"
    )
    
    parser.add_argument(
        "--host",
        type=str,
        default="localhost",
        help="Host for frontend (default: localhost)"
    )
    
    parser.add_argument(
        "--wandb",
        action="store_true",
        help="Use Weights & Biases for training"
    )
    
    args = parser.parse_args()
    
    try:
        if args.command == "frontend":
            run_frontend(args.port, args.host)
        elif args.command == "data":
            run_data_download()
        elif args.command == "sft":
            run_sft_training(args.wandb)
        elif args.command == "grpo":
            run_grpo_training(args.wandb)
        elif args.command == "test":
            run_model_test()
        elif args.command == "setup":
            run_setup_test()
        elif args.command == "pipeline":
            run_full_pipeline(args.wandb)
        elif args.command == "status":
            print_status()
        
    except KeyboardInterrupt:
        logger.info("Operation cancelled by user")
    except Exception as e:
        logger.error(f"Error running command '{args.command}': {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
