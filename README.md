# GRAG - 基于GRPO优化的医疗RAG系统
# GRAG - Medical RAG System with GRPO Optimization

一个基于中文医疗数据集的综合性RAG（检索增强生成）系统，采用SFT + GRPO微调策略。
A comprehensive RAG (Retrieval-Augmented Generation) system for medical consultation using Chinese medical datasets with SFT + GRPO fine-tuning.

## 🌟 主要特性 / Key Features

- **🤖 先进模型架构**: 基于Qwen3-8B的SFT + GRPO微调
- **🔍 智能检索系统**: 向量检索 + 重排序 + 自我修正机制
- **🌐 网络搜索集成**: 获取2025年最新医疗知识
- **🏥 医疗专用优化**: 针对中文医疗场景的专门优化
- **📊 全面评估体系**: 多维度评估指标和医疗安全性检查
- **🎯 多重奖励模型**: 准确性、相关性、安全性、完整性评估
- **💻 友好前端界面**: Streamlit构建的直观用户界面
- **🔧 自我修正能力**: 动态调整检索参数和查询策略

## 🏗️ 系统架构 / System Architecture

```
GRAG/
├── configs/                 # 配置文件 / Configuration files
│   ├── config.yaml         # 主配置 / Main configuration
│   ├── model_config.yaml   # 模型配置 / Model configuration
│   └── reward_config.yaml  # 奖励模型配置 / Reward model config
├── data/                   # 数据存储 / Data storage
│   ├── raw/               # 原始数据 / Raw data
│   ├── processed/         # 处理后数据 / Processed data
│   └── embeddings/        # 向量数据 / Vector embeddings
├── models/                # 训练模型 / Trained models
│   ├── sft/              # SFT模型 / SFT models
│   ├── grpo/             # GRPO模型 / GRPO models
│   └── reward/           # 奖励模型 / Reward models
├── src/                   # 源代码 / Source code
│   ├── data_processing/   # 数据处理 / Data processing
│   ├── training/          # 训练模块 / Training modules
│   ├── retrieval/         # 检索系统 / Retrieval system
│   ├── generation/        # 文本生成 / Text generation
│   ├── evaluation/        # 评估模块 / Evaluation modules
│   ├── graph/            # LangGraph工作流 / LangGraph workflows
│   ├── utils/            # 工具函数 / Utility functions
│   └── frontend/         # 前端界面 / Frontend interface
├── scripts/              # 脚本文件 / Scripts
├── tests/                # 测试文件 / Tests
├── notebooks/            # Jupyter笔记本 / Jupyter notebooks
└── logs/                 # 日志文件 / Log files
```

## 🚀 快速开始 / Quick Start

### 方式一：Docker部署 (推荐) / Method 1: Docker Deployment (Recommended)

```bash
# 克隆仓库 / Clone repository
git clone <repository-url>
cd GRAG

# 设置环境变量 / Set up environment variables
cp .env.example .env
# 编辑 .env 文件，填入你的API密钥 / Edit .env file with your API keys

# 启动开发环境 / Start development environment
make docker-dev

# 或启动生产环境 / Or start production environment
make docker-prod
```

访问服务 / Access services:
- **主应用 / Main App**: http://localhost:8501
- **Jupyter Lab**: http://localhost:8888 (仅开发环境)
- **监控面板 / Monitoring**: http://localhost:3000 (仅生产环境)

### 方式二：本地安装 / Method 2: Local Installation

```bash
# 克隆仓库 / Clone repository
git clone <repository-url>
cd GRAG

# 创建虚拟环境 / Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖 / Install dependencies
pip install -r requirements.txt

# 设置环境变量 / Set up environment variables
cp .env.example .env
# 编辑 .env 文件，填入你的API密钥 / Edit .env file with your API keys
```

### 2. 测试系统设置 / Test System Setup

```bash
# 运行设置测试 / Run setup test
python scripts/test_setup.py
```

### 3. 数据下载和处理 / Data Download and Processing

```bash
# 下载并处理医疗数据集 / Download and process medical dataset
python scripts/download_data.py
```

### 4. 模型训练 / Model Training

```bash
# SFT训练 / SFT Training
python scripts/train_sft.py

# GRPO训练 / GRPO Training  
python scripts/train_grpo.py
```

### 5. 启动前端 / Launch Frontend

```bash
# 启动Streamlit应用 / Launch Streamlit app
streamlit run src/frontend/app.py
```

## 🔧 详细使用指南 / Detailed Usage Guide

### 📥 数据准备 / Data Preparation

```bash
# 下载并处理医疗数据集 / Download and process medical dataset
python scripts/download_data.py

# 可选参数 / Optional arguments
python scripts/download_data.py --dataset michaelwzhu/ChatMed_Consult_Dataset --output-dir data/processed --force
```

### 🎯 模型训练 / Model Training

#### SFT训练 / SFT Training
```bash
# 基础SFT训练 / Basic SFT training
python scripts/train_sft.py

# 使用Weights & Biases / With Weights & Biases
python scripts/train_sft.py --wandb

# 自定义参数 / Custom parameters
python scripts/train_sft.py --data-dir data/processed --output-dir models/my_sft --model Qwen/Qwen3-8B
```

#### GRPO训练 / GRPO Training
```bash
# 基础GRPO训练 / Basic GRPO training
python scripts/train_grpo.py

# 使用SFT模型 / Using SFT model
python scripts/train_grpo.py --sft-model models/sft --wandb

# 自定义步数 / Custom steps
python scripts/train_grpo.py --max-steps 500 --output-dir models/my_grpo
```

### 🧪 模型测试 / Model Testing

```bash
# 测试完整RAG系统 / Test full RAG system
python scripts/test_model.py

# 仅测试生成器 / Test generator only
python scripts/test_model.py --generator-only

# 保存测试结果 / Save test results
python scripts/test_model.py --output test_results.json
```

## 📊 技术栈 / Technology Stack

### 🤖 核心AI框架 / Core AI Frameworks
- **Qwen3-8B**: 基础大语言模型 / Base large language model
- **Unsloth**: 高效模型微调 / Efficient model fine-tuning
- **Transformers**: 模型加载和推理 / Model loading and inference
- **TRL**: 强化学习训练 / Reinforcement learning training
- **PEFT/LoRA**: 参数高效微调 / Parameter-efficient fine-tuning

### 🔍 检索和向量化 / Retrieval and Vectorization
- **FAISS**: 向量相似度搜索 / Vector similarity search
- **BGE-Large-ZH**: 中文文本嵌入 / Chinese text embeddings
- **BGE-Reranker**: 检索结果重排序 / Retrieval result reranking
- **Sentence-Transformers**: 语义嵌入 / Semantic embeddings

### 🌐 工作流和集成 / Workflow and Integration
- **LangGraph**: 复杂工作流编排 / Complex workflow orchestration
- **LangChain**: AI应用开发框架 / AI application development framework
- **Tavily**: 网络搜索API / Web search API

### 💻 前端和可视化 / Frontend and Visualization
- **Streamlit**: 交互式Web应用 / Interactive web application
- **Plotly**: 数据可视化 / Data visualization
- **Altair**: 统计图表 / Statistical charts

### 📊 评估和监控 / Evaluation and Monitoring
- **ROUGE/BLEU**: 文本生成评估 / Text generation evaluation
- **BERTScore**: 语义相似度评估 / Semantic similarity evaluation
- **Weights & Biases**: 实验跟踪 / Experiment tracking
- **RAGAS**: RAG系统专用评估 / RAG-specific evaluation

### 🛠️ 开发工具 / Development Tools
- **Python 3.8+**: 主要编程语言 / Main programming language
- **PyTorch**: 深度学习框架 / Deep learning framework
- **Jupyter**: 交互式开发 / Interactive development
- **Git**: 版本控制 / Version control

### 🐳 部署和容器化 / Deployment and Containerization
- **Docker**: 容器化部署 / Containerized deployment
- **Docker Compose**: 多服务编排 / Multi-service orchestration
- **Nginx**: 反向代理 / Reverse proxy
- **Redis**: 缓存服务 / Caching service
- **Prometheus + Grafana**: 监控和可视化 / Monitoring and visualization

### 核心模型 / Core Models
- **基础模型 / Base Model**: Qwen3-8B
- **嵌入模型 / Embedding Model**: BAAI/bge-large-zh-v1.5  
- **重排序模型 / Reranker**: BAAI/bge-reranker-large
- **微调框架 / Fine-tuning**: Unsloth + GRPO

### 检索系统 / Retrieval System
- **向量数据库 / Vector DB**: FAISS
- **检索策略 / Retrieval Strategy**: 向量检索 + 重排序 + 自我修正
- **网络搜索 / Web Search**: Tavily API
- **工作流引擎 / Workflow Engine**: LangGraph

### 数据处理 / Data Processing
- **数据集 / Dataset**: michaelwzhu/ChatMed_Consult_Dataset
- **文本处理 / Text Processing**: jieba + OpenCC
- **数据增强 / Data Augmentation**: 同义词替换 + 改写 + 问题变体

## 🎯 核心功能 / Core Features

### 智能检索 / Intelligent Retrieval
- **多阶段检索**: 初始检索 → 重排序 → 自我修正
- **自适应阈值**: 根据查询类型动态调整相似度阈值
- **查询扩展**: 基于医疗实体的智能查询扩展
- **网络搜索回退**: 本地知识不足时自动搜索最新信息

### GRPO奖励学习 / GRPO Reward Learning
- **医疗准确性奖励**: 基于医学知识的准确性评估
- **相关性奖励**: 语义相似度和实体匹配
- **安全性奖励**: 医疗安全规则检查
- **完整性奖励**: 回答的全面性和结构化程度
- **格式合规性奖励**: 医疗回复的标准格式检查

### 自我修正机制 / Self-Correction Mechanism
- **查询扩展**: 添加医疗同义词和相关术语
- **参数调整**: 动态调整检索参数
- **网络搜索回退**: 本地检索不佳时启用网络搜索
- **多轮迭代**: 最多3轮修正以获得最佳结果

## 📋 环境要求 / Requirements

### 硬件要求 / Hardware Requirements
- **GPU**: 推荐NVIDIA GPU (8GB+ VRAM)
- **内存**: 16GB+ RAM
- **存储**: 50GB+ 可用空间

### 软件要求 / Software Requirements
- **Python**: 3.8+
- **CUDA**: 11.8+ (如使用GPU)
- **操作系统**: Linux/macOS/Windows

## 🔧 配置说明 / Configuration

### Docker配置 / Docker Configuration

Docker相关文件位于 `docker/` 目录：
- `Dockerfile`: 主要镜像定义
- `docker-compose.yml`: 基础服务配置
- `docker-compose.dev.yml`: 开发环境覆盖
- `docker-compose.prod.yml`: 生产环境覆盖

详细的Docker部署指南请参考 [docker/README.md](docker/README.md)

### 应用配置 / Application Configuration

主要配置文件位于 `configs/config.yaml`，包含：

### 模型配置 / Model Configuration
```yaml
model:
  base_model: "Qwen/Qwen3-8B"
  load_in_4bit: true
  lora_config:
    r: 32
    lora_alpha: 64
```

### 训练配置 / Training Configuration
```yaml
training:
  sft:
    num_train_epochs: 3
    learning_rate: 2e-4
  grpo:
    num_train_epochs: 1
    learning_rate: 5e-6
    reward_functions:
      - "medical_accuracy"
      - "relevance"
      - "safety"
```

### 检索配置 / Retrieval Configuration
```yaml
retrieval:
  vector_db:
    type: "faiss"
    dimension: 768
  embedding:
    model_name: "BAAI/bge-large-zh-v1.5"
  reranking:
    enabled: true
    model_name: "BAAI/bge-reranker-large"
```

## 📈 评估指标 / Evaluation Metrics

### 自动评估 / Automatic Evaluation
- **ROUGE**: 文本重叠度评估
- **BLEU**: 机器翻译质量评估
- **BERTScore**: 语义相似度评估
- **检索精确度/召回率**: 检索系统性能

### 医疗专用评估 / Medical-Specific Evaluation
- **症状理解准确性**: 对患者症状的理解程度
- **诊断建议合理性**: 诊断建议的医学合理性
- **治疗建议安全性**: 治疗建议的安全性评估
- **风险评估能力**: 对医疗风险的识别能力

## 🤝 贡献指南 / Contributing

欢迎贡献代码！请遵循以下步骤：

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证 / License

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢 / Acknowledgments

- [Qwen3-8B](https://huggingface.co/Qwen/Qwen3-8B) - 基础语言模型
- [ChatMed_Consult_Dataset](https://huggingface.co/datasets/michaelwzhu/ChatMed_Consult_Dataset) - 中文医疗数据集
- [BGE Embeddings](https://huggingface.co/BAAI/bge-large-zh-v1.5) - 中文嵌入模型
- [Unsloth](https://github.com/unslothai/unsloth) - 高效微调框架
- [LangGraph](https://github.com/langchain-ai/langgraph) - 工作流引擎

## 📞 联系方式 / Contact

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发送邮件至：[<EMAIL>]
- 加入讨论群：[群号或链接]

---

**注意**: 本系统仅供学习和研究使用，不能替代专业医疗诊断和治疗建议。
**Note**: This system is for educational and research purposes only and cannot replace professional medical diagnosis and treatment advice.
