# -*- coding: utf-8 -*-
"""
Enhanced Vector Store Module / 增强向量存储模块

This module implements an enhanced FAISS-based vector storage with medical-specific features.
该模块实现增强的基于FAISS的向量存储，具有医疗特定功能。
"""

import numpy as np
import faiss
import pickle
import json
from typing import List, Dict, Tuple, Optional, Any
from pathlib import Path
from sentence_transformers import SentenceTransformer
try:
    from FlagEmbedding import FlagModel
    HAS_FLAG_EMBEDDING = True
except ImportError:
    HAS_FLAG_EMBEDDING = False
    
try:
    from ..utils import get_logger, get_config_manager, batch_process
except ImportError:
    # Fallback for direct execution
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from utils import get_logger, get_config_manager, batch_process

logger = get_logger(__name__)


class MedicalVectorStore:
    """Enhanced medical vector store using FAISS / 使用FAISS的增强医疗向量存储"""
    
    def __init__(self, embedding_model: str = None, dimension: int = None):
        """
        Initialize FAISS vector store / 初始化FAISS向量存储
        
        Args:
            embedding_model: Name of the embedding model / 嵌入模型名称
            dimension: Dimension of the embeddings / 嵌入维度
        """
        # Get config from global config manager / 从全局配置管理器获取配置
        config_manager = get_config_manager()
        retrieval_config = config_manager.retrieval
        
        self.embedding_model_name = embedding_model or retrieval_config.embedding["model_name"]
        self.dimension = dimension or retrieval_config.vector_db["dimension"]
        self.batch_size = retrieval_config.embedding.get("batch_size", 32)
        self.normalize_embeddings = retrieval_config.embedding.get("normalize_embeddings", True)
        
        self.embedding_model = None
        self.index = None
        self.documents = []
        self.metadata = []
        
        logger.info(f"Initialized vector store with model: {self.embedding_model_name}")
        
    def load_embedding_model(self):
        """Load the embedding model / 加载嵌入模型"""
        logger.info(f"Loading embedding model: {self.embedding_model_name}")
        
        try:
            # Try to load as FlagEmbedding model first (better for Chinese) / 首先尝试加载FlagEmbedding模型
            if HAS_FLAG_EMBEDDING and "bge" in self.embedding_model_name.lower():
                self.embedding_model = FlagModel(
                    self.embedding_model_name,
                    query_instruction_for_retrieval="为这个句子生成表示以用于检索相关文章：",
                    use_fp16=True
                )
                logger.info("Loaded FlagEmbedding model")
            else:
                # Fallback to SentenceTransformers / 回退到SentenceTransformers
                self.embedding_model = SentenceTransformer(self.embedding_model_name)
                logger.info("Loaded SentenceTransformer model")
                
        except Exception as e:
            logger.warning(f"Failed to load FlagEmbedding, using SentenceTransformer: {e}")
            self.embedding_model = SentenceTransformer(self.embedding_model_name)
        
        logger.info("Embedding model loaded successfully")
    
    def create_index(self, index_type: str = None):
        """
        Create FAISS index / 创建FAISS索引
        
        Args:
            index_type: Type of FAISS index to create / 要创建的FAISS索引类型
        """
        if index_type is None:
            config_manager = get_config_manager()
            index_type = config_manager.retrieval.vector_db.get("index_type", "IndexFlatIP")
            
        logger.info(f"Creating FAISS index: {index_type}")
        
        if index_type == "IndexFlatIP":
            self.index = faiss.IndexFlatIP(self.dimension)
        elif index_type == "IndexFlatL2":
            self.index = faiss.IndexFlatL2(self.dimension)
        elif index_type == "IndexIVFFlat":
            quantizer = faiss.IndexFlatL2(self.dimension)
            self.index = faiss.IndexIVFFlat(quantizer, self.dimension, 100)
        elif index_type == "IndexHNSWFlat":
            self.index = faiss.IndexHNSWFlat(self.dimension, 32)
        else:
            raise ValueError(f"Unsupported index type: {index_type}")
        
        logger.info("FAISS index created successfully")
    
    def encode_texts(self, texts: List[str]) -> np.ndarray:
        """
        Encode texts to embeddings / 将文本编码为嵌入

        Args:
            texts: List of texts to encode / 要编码的文本列表

        Returns:
            Embeddings array / 嵌入数组
        """
        try:
            # Filter out empty texts / 过滤空文本
            non_empty_texts = [text for text in texts if text and text.strip()]
            if len(non_empty_texts) != len(texts):
                logger.warning(f"Found {len(texts) - len(non_empty_texts)} empty texts, using placeholder")
                # Replace empty texts with placeholder / 用占位符替换空文本
                processed_texts = [text if text and text.strip() else "空文本" for text in texts]
            else:
                processed_texts = texts

            if HAS_FLAG_EMBEDDING and hasattr(self.embedding_model, 'encode') and hasattr(self.embedding_model, 'query_instruction_for_retrieval'):
                # Use FlagEmbedding / 使用FlagEmbedding
                logger.debug(f"Using FlagEmbedding to encode {len(processed_texts)} texts")
                embeddings = self.embedding_model.encode(processed_texts)
            else:
                # Use SentenceTransformer / 使用SentenceTransformer
                logger.debug(f"Using SentenceTransformer to encode {len(processed_texts)} texts")
                embeddings = self.embedding_model.encode(
                    processed_texts,
                    normalize_embeddings=self.normalize_embeddings,
                    batch_size=min(self.batch_size, len(processed_texts)),
                    show_progress_bar=False  # Disable progress bar to avoid clutter
                )

            return embeddings.astype(np.float32)

        except Exception as e:
            logger.error(f"Error encoding texts: {e}")
            logger.error(f"Text sample: {texts[0][:100] if texts else 'No texts'}")
            raise
    
    def add_documents(self, documents: List[Dict], batch_size: int = None):
        """
        Add documents to the vector store / 向向量存储添加文档

        Args:
            documents: List of document dictionaries / 文档字典列表
            batch_size: Batch size for processing / 处理批次大小
        """
        if self.embedding_model is None:
            self.load_embedding_model()

        batch_size = batch_size or self.batch_size
        logger.info(f"Adding {len(documents)} documents to vector store in batches of {batch_size}")

        # Process documents in batches / 批量处理文档
        first_batch = True
        total_processed = 0

        try:
            for batch_idx, batch in enumerate(batch_process(documents, batch_size)):
                logger.info(f"Processing batch {batch_idx + 1}/{(len(documents) + batch_size - 1) // batch_size}")

                # Extract texts for embedding / 提取文本用于嵌入
                texts = [doc.get("text", "") for doc in batch]

                # Skip empty texts / 跳过空文本
                if not any(texts):
                    logger.warning(f"Batch {batch_idx + 1} contains only empty texts, skipping...")
                    continue

                # Generate embeddings / 生成嵌入
                logger.debug(f"Generating embeddings for {len(texts)} texts...")
                embeddings = self.encode_texts(texts)
                logger.debug(f"Generated embeddings with shape: {embeddings.shape}")

                # Create index with correct dimension on first batch / 在第一批时使用正确的维度创建索引
                if first_batch:
                    actual_dimension = embeddings.shape[1]
                    if self.dimension != actual_dimension:
                        logger.warning(f"Updating dimension from {self.dimension} to {actual_dimension}")
                        self.dimension = actual_dimension

                    if self.index is None:
                        self.create_index()
                    first_batch = False

                # Add to FAISS index / 添加到FAISS索引
                logger.debug(f"Adding {len(embeddings)} embeddings to FAISS index...")
                self.index.add(embeddings)

                # Store documents and metadata / 存储文档和元数据
                self.documents.extend(batch)
                self.metadata.extend([doc.get("metadata", {}) for doc in batch])

                total_processed += len(batch)
                logger.info(f"Processed {total_processed}/{len(documents)} documents")

                # Force garbage collection to free memory / 强制垃圾回收以释放内存
                import gc
                gc.collect()

        except Exception as e:
            logger.error(f"Error processing batch {batch_idx + 1}: {e}")
            raise

        logger.info(f"Successfully added {len(documents)} documents. Total: {len(self.documents)}")
    
    def search(self, query: str, top_k: int = None, 
               similarity_threshold: float = None) -> List[Tuple[Dict, float]]:
        """
        Search for similar documents / 搜索相似文档
        
        Args:
            query: Search query / 搜索查询
            top_k: Number of top results to return / 返回的顶部结果数量
            similarity_threshold: Minimum similarity threshold / 最小相似度阈值
            
        Returns:
            List of (document, score) tuples / (文档, 分数)元组列表
        """
        if self.embedding_model is None or self.index is None:
            raise ValueError("Vector store not initialized. Add documents first.")
        
        # Get default values from config / 从配置获取默认值
        config_manager = get_config_manager()
        search_config = config_manager.retrieval.search
        top_k = top_k or search_config.get("top_k", 10)
        similarity_threshold = similarity_threshold or search_config.get("similarity_threshold", 0.7)
        
        # Generate query embedding / 生成查询嵌入
        query_embedding = self.encode_texts([query])
        
        # Search in FAISS index / 在FAISS索引中搜索
        scores, indices = self.index.search(query_embedding, top_k)
        
        # Prepare results / 准备结果
        results = []
        for score, idx in zip(scores[0], indices[0]):
            if idx < len(self.documents) and score >= similarity_threshold:
                results.append((self.documents[idx], float(score)))
        
        logger.debug(f"Found {len(results)} documents above threshold {similarity_threshold}")
        return results
    
    def save(self, save_path: str):
        """
        Save the vector store to disk / 保存向量存储到磁盘
        
        Args:
            save_path: Path to save the vector store / 保存向量存储的路径
        """
        save_path = Path(save_path)
        save_path.mkdir(parents=True, exist_ok=True)
        
        # Save FAISS index / 保存FAISS索引
        faiss.write_index(self.index, str(save_path / "index.faiss"))
        
        # Save documents and metadata / 保存文档和元数据
        with open(save_path / "documents.pkl", "wb") as f:
            pickle.dump(self.documents, f)
        
        with open(save_path / "metadata.pkl", "wb") as f:
            pickle.dump(self.metadata, f)
        
        # Save configuration / 保存配置
        config = {
            "embedding_model": self.embedding_model_name,
            "dimension": self.dimension,
            "num_documents": len(self.documents)
        }
        
        with open(save_path / "config.json", "w", encoding="utf-8") as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Vector store saved to {save_path}")
    
    def load(self, load_path: str):
        """
        Load the vector store from disk / 从磁盘加载向量存储
        
        Args:
            load_path: Path to load the vector store from / 加载向量存储的路径
        """
        load_path = Path(load_path)
        
        if not load_path.exists():
            raise FileNotFoundError(f"Vector store not found at {load_path}")
        
        # Load configuration / 加载配置
        with open(load_path / "config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        
        self.embedding_model_name = config["embedding_model"]
        self.dimension = config["dimension"]
        
        # Load embedding model / 加载嵌入模型
        self.load_embedding_model()
        
        # Load FAISS index / 加载FAISS索引
        self.index = faiss.read_index(str(load_path / "index.faiss"))
        
        # Load documents and metadata / 加载文档和元数据
        with open(load_path / "documents.pkl", "rb") as f:
            self.documents = pickle.load(f)
        
        with open(load_path / "metadata.pkl", "rb") as f:
            self.metadata = pickle.load(f)
        
        logger.info(f"Vector store loaded from {load_path}")
        logger.info(f"Loaded {len(self.documents)} documents")


def create_vector_store_from_knowledge_base(
    knowledge_base_path: str, 
    vector_store_path: str,
    embedding_model: str = None
) -> MedicalVectorStore:
    """
    Create vector store from knowledge base file / 从知识库文件创建向量存储
    
    Args:
        knowledge_base_path: Path to knowledge base JSONL file / 知识库JSONL文件路径
        vector_store_path: Path to save vector store / 保存向量存储的路径
        embedding_model: Embedding model to use / 要使用的嵌入模型
        
    Returns:
        Created vector store / 创建的向量存储
    """
    # Load knowledge base / 加载知识库
    documents = []
    with open(knowledge_base_path, "r", encoding="utf-8") as f:
        for line in f:
            documents.append(json.loads(line.strip()))
    
    # Create vector store / 创建向量存储
    vector_store = MedicalVectorStore(embedding_model=embedding_model)
    vector_store.add_documents(documents)
    vector_store.save(vector_store_path)
    
    logger.info(f"Created vector store with {len(documents)} documents")
    return vector_store
