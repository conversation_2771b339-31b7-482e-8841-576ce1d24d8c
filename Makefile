# GRAG System Makefile / GRAG系统Makefile
# Provides convenient commands for development and deployment
# 为开发和部署提供便捷命令

.PHONY: help install setup data train-sft train-grpo train-all test frontend clean status docker-build docker-run docker-dev docker-prod docker-stop docker-logs

# Default target / 默认目标
help:
	@echo "GRAG System - Medical RAG with GRPO Optimization"
	@echo "=================================================="
	@echo ""
	@echo "Available commands / 可用命令:"
	@echo ""
	@echo "Setup / 设置:"
	@echo "  make install     - Install dependencies / 安装依赖"
	@echo "  make setup       - Run setup test / 运行设置测试"
	@echo "  make status      - Show system status / 显示系统状态"
	@echo ""
	@echo "Data / 数据:"
	@echo "  make data        - Download and process data / 下载和处理数据"
	@echo ""
	@echo "Training / 训练:"
	@echo "  make train-sft   - Train SFT model / 训练SFT模型"
	@echo "  make train-grpo  - Train GRPO model / 训练GRPO模型"
	@echo "  make train-all   - Run full training pipeline / 运行完整训练流水线"
	@echo ""
	@echo "Testing / 测试:"
	@echo "  make test        - Test trained model / 测试训练模型"
	@echo ""
	@echo "Frontend / 前端:"
	@echo "  make frontend    - Start web interface / 启动Web界面"
	@echo ""
	@echo "Docker / Docker:"
	@echo "  make docker-dev  - Start development environment / 启动开发环境"
	@echo "  make docker-prod - Start production environment / 启动生产环境"
	@echo "  make docker-stop - Stop all containers / 停止所有容器"
	@echo "  make docker-logs - View container logs / 查看容器日志"
	@echo ""
	@echo "Maintenance / 维护:"
	@echo "  make clean       - Clean generated files / 清理生成文件"
	@echo "  make clean-all   - Clean everything / 清理所有内容"
	@echo ""

# Install dependencies / 安装依赖
install:
	@echo "Installing dependencies / 安装依赖..."
	pip install -r requirements.txt
	@echo "Dependencies installed successfully / 依赖安装成功"

# Run setup test / 运行设置测试
setup:
	@echo "Running setup test / 运行设置测试..."
	python scripts/test_setup.py

# Show system status / 显示系统状态
status:
	@echo "Checking system status / 检查系统状态..."
	python scripts/run_app.py status

# Download and process data / 下载和处理数据
data:
	@echo "Downloading and processing data / 下载和处理数据..."
	python scripts/run_app.py data

# Train SFT model / 训练SFT模型
train-sft:
	@echo "Training SFT model / 训练SFT模型..."
	python scripts/run_app.py sft

# Train SFT model with W&B / 使用W&B训练SFT模型
train-sft-wandb:
	@echo "Training SFT model with W&B / 使用W&B训练SFT模型..."
	python scripts/run_app.py sft --wandb

# Train GRPO model / 训练GRPO模型
train-grpo:
	@echo "Training GRPO model / 训练GRPO模型..."
	python scripts/run_app.py grpo

# Train GRPO model with W&B / 使用W&B训练GRPO模型
train-grpo-wandb:
	@echo "Training GRPO model with W&B / 使用W&B训练GRPO模型..."
	python scripts/run_app.py grpo --wandb

# Run full training pipeline / 运行完整训练流水线
train-all:
	@echo "Running full training pipeline / 运行完整训练流水线..."
	python scripts/run_app.py pipeline

# Run full training pipeline with W&B / 使用W&B运行完整训练流水线
train-all-wandb:
	@echo "Running full training pipeline with W&B / 使用W&B运行完整训练流水线..."
	python scripts/run_app.py pipeline --wandb

# Test trained model / 测试训练模型
test:
	@echo "Testing trained model / 测试训练模型..."
	python scripts/run_app.py test

# Start web interface / 启动Web界面
frontend:
	@echo "Starting web interface / 启动Web界面..."
	@echo "Open http://localhost:8501 in your browser / 在浏览器中打开 http://localhost:8501"
	python scripts/run_app.py frontend

# Start web interface on custom port / 在自定义端口启动Web界面
frontend-port:
	@echo "Starting web interface on port $(PORT) / 在端口$(PORT)启动Web界面..."
	@echo "Open http://localhost:$(PORT) in your browser / 在浏览器中打开 http://localhost:$(PORT)"
	python scripts/run_app.py frontend --port $(PORT)

# Development commands / 开发命令

# Run tests / 运行测试
test-unit:
	@echo "Running unit tests / 运行单元测试..."
	python -m pytest tests/ -v

# Format code / 格式化代码
format:
	@echo "Formatting code / 格式化代码..."
	black src/ scripts/ tests/
	@echo "Code formatted / 代码已格式化"

# Lint code / 代码检查
lint:
	@echo "Linting code / 检查代码..."
	flake8 src/ scripts/ tests/
	@echo "Linting completed / 代码检查完成"

# Type check / 类型检查
typecheck:
	@echo "Running type check / 运行类型检查..."
	mypy src/ --ignore-missing-imports
	@echo "Type check completed / 类型检查完成"

# Clean generated files / 清理生成文件
clean:
	@echo "Cleaning generated files / 清理生成文件..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf build/
	rm -rf dist/
	@echo "Generated files cleaned / 生成文件已清理"

# Clean everything including models and data / 清理所有内容包括模型和数据
clean-all: clean
	@echo "Cleaning all data and models / 清理所有数据和模型..."
	@read -p "This will delete all processed data and trained models. Continue? (y/N): " confirm && [ "$$confirm" = "y" ]
	rm -rf data/processed/
	rm -rf data/embeddings/
	rm -rf models/
	rm -rf logs/
	@echo "All data and models cleaned / 所有数据和模型已清理"

# Docker commands / Docker命令

# Build Docker image / 构建Docker镜像
docker-build:
	@echo "Building Docker image / 构建Docker镜像..."
	cd docker && docker build -f Dockerfile -t grag-system ..

# Run Docker container / 运行Docker容器
docker-run:
	@echo "Running Docker container / 运行Docker容器..."
	docker run -p 8501:8501 -v $(PWD)/data:/app/data -v $(PWD)/models:/app/models grag-system

# Start development environment / 启动开发环境
docker-dev:
	@echo "Starting development environment / 启动开发环境..."
	@echo "Services will be available at:"
	@echo "  - Streamlit: http://localhost:8501"
	@echo "  - Jupyter: http://localhost:8888"
	cd docker && docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build

# Start production environment / 启动生产环境
docker-prod:
	@echo "Starting production environment / 启动生产环境..."
	@echo "Services will be available at:"
	@echo "  - Main App: http://localhost:8501"
	@echo "  - Monitoring: http://localhost:3000"
	cd docker && docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --build

# Stop all containers / 停止所有容器
docker-stop:
	@echo "Stopping all containers / 停止所有容器..."
	cd docker && docker-compose down

# View container logs / 查看容器日志
docker-logs:
	@echo "Viewing container logs / 查看容器日志..."
	cd docker && docker-compose logs -f

# Clean Docker resources / 清理Docker资源
docker-clean:
	@echo "Cleaning Docker resources / 清理Docker资源..."
	docker system prune -f
	docker volume prune -f

# Restart Docker services / 重启Docker服务
docker-restart:
	@echo "Restarting Docker services / 重启Docker服务..."
	cd docker && docker-compose restart

# Development environment / 开发环境

# Create virtual environment / 创建虚拟环境
venv:
	@echo "Creating virtual environment / 创建虚拟环境..."
	python -m venv venv
	@echo "Virtual environment created / 虚拟环境已创建"
	@echo "Activate with: source venv/bin/activate (Linux/Mac) or venv\\Scripts\\activate (Windows)"

# Install development dependencies / 安装开发依赖
install-dev: install
	@echo "Installing development dependencies / 安装开发依赖..."
	pip install pytest black flake8 mypy pre-commit
	pre-commit install
	@echo "Development dependencies installed / 开发依赖已安装"

# Quick start / 快速开始
quickstart: install setup data train-all test
	@echo ""
	@echo "🎉 GRAG System setup completed! / GRAG系统设置完成!"
	@echo ""
	@echo "Next steps / 下一步:"
	@echo "1. Start the frontend: make frontend"
	@echo "2. Open http://localhost:8501 in your browser"
	@echo "3. Start asking medical questions!"
	@echo ""

# Show configuration / 显示配置
config:
	@echo "Current configuration / 当前配置:"
	@echo "Python version: $(shell python --version)"
	@echo "Pip version: $(shell pip --version)"
	@echo "Current directory: $(PWD)"
	@echo "Virtual environment: $(VIRTUAL_ENV)"

# Variables for customization / 自定义变量
PORT ?= 8501
HOST ?= localhost
WANDB ?= false

# Help for specific targets / 特定目标的帮助
help-training:
	@echo "Training Commands Help / 训练命令帮助:"
	@echo ""
	@echo "make train-sft       - Train Supervised Fine-Tuning model"
	@echo "make train-grpo      - Train Group Relative Policy Optimization model"
	@echo "make train-all       - Run complete training pipeline (data + SFT + GRPO)"
	@echo ""
	@echo "With Weights & Biases logging:"
	@echo "make train-sft-wandb"
	@echo "make train-grpo-wandb"
	@echo "make train-all-wandb"

help-data:
	@echo "Data Commands Help / 数据命令帮助:"
	@echo ""
	@echo "make data            - Download and process medical dataset"
	@echo "                      Downloads from: michaelwzhu/ChatMed_Consult_Dataset"
	@echo "                      Processes and creates embeddings"
	@echo "                      Creates knowledge base for retrieval"
